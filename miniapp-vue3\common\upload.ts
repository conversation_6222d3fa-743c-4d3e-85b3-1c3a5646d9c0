import ajax from "./ajax";

export const uploadUrl = 'https://up-z2.qiniup.com'; //上传地址
export const uploadedUrl = 'http://tu.1caiba.com/'; //上传后文件地址
export let uploadPolicyInfo:any = null;

// type imgModel:{
// 	progress:number,
// 	filePath:string,
// 	key:string
// }

						
export const uploadQn = async (imgModel:any	,path:any) => {
	return new Promise(async (r, j) => {
		if (!uploadPolicyInfo || !uploadPolicyInfo.token) {
			uploadPolicyInfo = {
				key: '',
				token: '',
				url: ''
			};
			uploadPolicyInfo.token = await ajax.get('//public/getQnToken');
		}
		uploadPolicyInfo.key = path + '/' + new Date().getTime() + imgModel.filePath.substr(imgModel.filePath.lastIndexOf('.'))
		let uploadParams = {
			name: 'file',
			url: uploadUrl,
			filePath: imgModel.filePath,
			formData: {
				key: uploadPolicyInfo.key,
				token: uploadPolicyInfo.token,
				success_action_status: '200',
				// 'Signature': uploadPolicyInfo.Signature,
				// 'x-cos-security-token': uploadPolicyInfo.XCosSecurityToken,
				'Content-Type': 'application/json',
			},
		}

		const uploadTask = uni.uploadFile({
			...uploadParams,
			success: (res) => {
				if (res.data) {
					r(uploadedUrl + uploadParams.formData.key)
				} else {
					j(res);
				}
			},
			fail: (err) => {
				j(err.errMsg)
			}
		})
		//上传进度
		uploadTask.onProgressUpdate((progressRes) => {
			imgModel.progress = progressRes.progress;
		});
	})
}

//上传到后台
export const uploadApi = async (imgModel:any,path:string,progressAct:any) => {
	return new Promise((resolve, reject) => {
		//发送给后端的附加参数
		const formData = {
			thumb_mode: 1,
		};
		let uploadTask = uni.uploadFile({
			url: uploadUrl,
			filePath: imgModel.filePath,
			name: 'file',
			formData: formData,
			success(uploadFileResult) {
				const uploadFileRes = JSON.parse(uploadFileResult.data) || {};
				if (uploadFileRes.status === 1 && uploadFileRes.data) {
					resolve(uploadFileRes.data);
				} else {
					reject('接口返回错误');
				}
			},
			fail() {
				reject('网络链接错误');
			}
		});
		//上传进度
		uploadTask.onProgressUpdate((progressRes) => {
			if (progressAct) {
				progressAct(progressRes.progress);
			}
		});
	});
}
//阿里
export const uploadOss = async (imgModel,path) => {

	return new Promise(async (r, j) => {

		if (!uploadPolicyInfo || (Date.now() / 1000 + 30) > uploadPolicyInfo.expiredTime) {
			const r2 = await ajax.get<any>('/upload/cosParams');
			//todo AuthData
			const AuthData = {
				SecretId: r2.credential.credentials.tmpSecretId,
				SecretKey: r2.credential.credentials.tmpSecretKey,
				Method: 'POST',
				Pathname: "/" //+r2.path
			};
			uploadPolicyInfo = {
				path: "/" + r2.path,
				url: 'https://' + r2.bucket + '.cos.' + r2.region + '.myqcloud.com/',
				Signature: AuthData,
				'XCosSecurityToken': r2.credential.credentials.sessionToken,
				expiredTime: r2.credential.expiredTime
			};
		}

		imgModel.key = uploadPolicyInfo.path + imgModel.key + imgModel.filePath.substr(imgModel
			.filePath.lastIndexOf(
				'.'))

		let uploadParams = {
			name: 'file',
			url: uploadPolicyInfo.url,
			filePath: imgModel.filePath,
			formData: {
				key: imgModel.key,
				success_action_status: '200',
				'Signature': uploadPolicyInfo.Signature,
				'x-cos-security-token': uploadPolicyInfo.XCosSecurityToken,
				'Content-Type': '',
			},
		}

		// console.log('uploadOss =>')
		// console.log(uploadParams)
		const uploadTask = uni.uploadFile({
			...uploadParams,
			success: (res:any) => {
				console.log('success uploadOss=>' + res.header.Location)
				if (res.statusCode == 200) {
					r(res.header.Location)
				} else {
					j(res);
				}
			},
			fail: (err) => {
				j(err.errMsg)
			}
		})

		//上传进度
		uploadTask.onProgressUpdate((progressRes) => {
			imgModel.progress = progressRes.progress;
		});
	})

}

// //腾讯云
// export const uploadCos = async (imgModel,path) => {
// 	if (!uploadPolicyInfo) {
// 		const r2 = await ajax.get<any>('/upload/cosParams');
// 		uploadPolicyInfo = {
// 			path: "/" + r2.path,
// 			region: r2.region,
// 			bucket: r2.bucket,
// 		};
// 	}

// 	return new Promise(async (r, j) => {
// 		imgModel.key = uploadPolicyInfo.path + imgModel.key
		
// 		cos.postObject({
// 			Bucket: uploadPolicyInfo.bucket,
// 			Region: uploadPolicyInfo.region,
// 			Key: imgModel.key,
// 			FilePath: imgModel.filePath,
// 			onProgress: info => {
// 				imgModel.progress = info.percent
// 			}
// 		}, function(err, data) {
// 			console.log(err || data);
// 		});
// 	})

// }