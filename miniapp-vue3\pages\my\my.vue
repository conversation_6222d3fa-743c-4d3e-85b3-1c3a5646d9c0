<template>
	<view class="page-imgbg" :style="{'background-image':$asset('bg','bg')}" style="min-height:100vh;overflow: hidden; padding-top: 260rpx;">
		<view class="sheader" :style="{'background-image':$asset('my_head_bg','bg')}">
			<view class="row-flex-auto" style="position: absolute;left: 220rpx;top: 120rpx;">
				<myAvatar></myAvatar>
				<view style="margin-left: 20rpx;" class="f-big nowrap">
					<view class="f-big nowrap" style="color:#fff;width: 300rpx;">{{ user.nickName }}</view>
					<view class="f-color-light f-sm nowrap" style="width: 300rpx;">
						{{ user.sign || "" }}
					</view>
				</view>
			</view>
		</view>

		<view class="container">
			<view class="row-flex-around" style="padding: 30rpx 0px">
				<navigator class="btn-box row-flex-justify" url="Posts" v-if="sys.allowUserPost == 1">
					<view class="iconfont iconedit"></view>
					<view class="">
						<view class="">我的</view>
						<view class="">动态</view>
					</view>
				</navigator>
				<view class="middle-line" v-if="sys.allowUserPost == 1"></view>
				<navigator class="btn-box row-flex-justify" url="Visits">
					<view class="iconfont iconicon_list_daohang"></view>
					<view class="">
						<view class="">我的</view>
						<view class="">轨迹</view>
					</view>
				</navigator>
				<view class="middle-line"></view>
				<navigator class="btn-box row-flex-justify" url="Gallery" v-if="sys.allowUserGallery == 1">
					<view class="iconfont iconxiangce1"></view>
					<view class="">
						<view class="">我的</view>
						<view class="">相册</view>
					</view>
				</navigator>
				<view class="middle-line" v-if="sys.allowUserGallery == 1"></view>
				<navigator class="btn-box row-flex-justify" url="Venue">
					<view class="iconfont iconqiuchang"></view>
					<view class="">
						<view class="">我的</view>
						<view class="">球场</view>
					</view>
				</navigator>
			</view>

			<!--动态-->
			<view class="my-title f-color-light" v-if="sys.allowUserPost == 1">最近动态</view>
			<view class="my-box dark" style="position: relative;min-height: 100rpx;" v-if="sys.allowUserPost == 1">
				<navigator class="newPost-btn g-hover" url="PostEdit">
					<view class="iconfont iconadd f-color-warn"></view>
				</navigator>
				<view :class="{'line-1':idx < (d.postTopData.length - 1)}" v-for="item,idx in d.postTopData" :d="item" :key="idx">
					<my-post-item :d="item"></my-post-item>
				</view>
			</view>
			
			<!--最近签到-->
			<view class="my-title f-color-light">最近签到</view>
			<view class="my-box dark" style="position: relative;min-height: 100rpx;line-height: 2.5;">
				<view :class="{'line-1':idx < (d.visitTopData.length - 1)}" v-for="item,idx in d.visitTopData" :d="item" :key="idx">
					<view class="row-flex-auto">
						<view class="f-time" style="width: 180rpx;">
							<text>{{ item.month }}月</text>
							<text>{{ item.day }} {{ item.time }}</text>
						</view>
						<view style="padding-left: 10rpx;word-break: break-all; line-height: 1.5;">
							拜访了 <text class="f-big">{{ item.venueName || "" }}</text>
						</view>
					</view>
				</view>
			</view>
			
			<view class="my-card-light">
				<myCell label="我的资料" arrow url="/pages/My/UserInfo" line="1"></myCell>
				<myCell v-if="user.accountType == 999" label="系统设置" arrow url="/pages/My/Manage"></myCell>
			</view>
		</view>

		<view class="center" style="padding: 30rpx;">
			<view v-if="sys.sysNotice" class="my-card" style="color: #fff;text-align: left">
				<view class="iconfont iconwarning f-color-warn" style="position: absolute;right: -16px;top: -10px;background: #302777;border-radius: 50%;"></view>
				<text style="display:inline-block">{{ sys.sysNotice }}</text>
			</view>
		</view>
		
		<view class="center pb">
			<button open-type="contact" class="my-button white" style="opacity: .6;">
				留言，联系我们
			</button>
		</view>
		
		<loginDoor></loginDoor>
	</view>
</template>

<script lang="ts" setup>
import { reactive, computed } from 'vue'
import { onLoad, onShareAppMessage, onShareTimeline, onPullDownRefresh } from '@dcloudio/uni-app'
import { appStore } from '../../store'
import { userStore } from '../../store/user'
import ajax from '../../common/ajax'
import { formatDate } from '../../common/funs'
const app = appStore()
const user = userStore()
const sys = computed(() => app.sys)

const d = reactive({
	postTopData: [] as any[],
	visitTopData: [] as any[]
})

onLoad(() => {
	wx.showShareMenu({
		withShareTicket: true,
		menus: ['shareAppMessage', 'shareTimeline']
	})
	
	if (user.info.token) {
		load()
	}
})

onShareTimeline(() => {
	return {
		title: `${user.info.nickName}的毽球空间`,
		query: `id=${user.info.id}`
	}
})

onShareAppMessage(() => {
	return {
		title: `${user.info.nickName}的毽球空间`,
		path: `/pages/User/User?id=${user.info.id}`
	}
})

onPullDownRefresh(() => {
	load()
	setTimeout(() => {
		uni.stopPullDownRefresh()
	}, 500)
})

function load() {
	loadPostTop()
	loadVisitTop()
	user.getUserAccount()
}

async function loadPostTop() {
	try {
		const res = await ajax.get<any>('/my/posts', { pageSize: 3 })
		d.postTopData = res.list || []
	} catch (e) {
		console.error('加载动态失败', e)
	}
}

async function loadVisitTop() {
	try {
		const res = await ajax.get<any>('/my/visits', { pageSize: 5 })
		res.list.forEach((v: any) => {
			if (!v.month) {
				const s = formatDate(v.updatedAt, 'MM,dd,hh:mm').split(",")
				v.month = s[0]
				v.day = s[1]
				v.time = s[2]
			}
		})
		d.visitTopData = res.list || []
	} catch (e) {
		console.error('加载签到记录失败', e)
	}
}
</script>

<style lang="scss" scoped>
	.newPost-btn{
		position: absolute;right: 10rpx;top:10rpx;background: rgba(110, 113, 149, 0.9);;
		width: 70rpx;
		text-align: center;
		line-height: 70rpx;
		height: 70rpx;
		z-index: 2;
	}
	.middle-line{
		width: 2rpx;
		background: rgba(255, 255, 255, 0.2);
		height: 50rpx;
	}
	.btn-box{
		line-height: 1.4;
		border-radius: 2rpx;
		color: rgba(255,255,255,.7);
		border: 0;
		border: 1rpx solid rgba(255, 255, 255, 0.0);
		background: transparent;
		background: linear-gradient(0deg, rgba(20, 9, 142, 0.0) 0, rgba(86, 1, 114, 0.0) 100%);
		padding: 10rpx 20rpx 10rpx 10rpx;
		transition: all .3s;
		
		&:active{
			background: rgba($color: white, $alpha: .3);
			color: rgba(255,255,255,1);
			transform: scale(1.1);
		}
		.iconfont{
			font-size: 2.8em;margin-right: 10rpx;
		}
	}
	.sheader{
		position: fixed;
		z-index: 12;
		top: 0;
		height: 260rpx;
		width: 100vw;
		background-repeat: no-repeat;
		background-size: cover;
		background-position:bottom center;
	}
	.list-item{
		padding: 15upx 0;
		display: block;
		border-bottom: 1upx dashed $uni-bg-color;
		&:last-child{
			border-bottom: 0;
		}
	}
	.status{
		display: flex;
		align-items: center;
		justify-content:flex-end;
		.iconfont{
			margin-right: 10upx;
		}
	}
	.remarkName{
		height:32upx;
	}
</style>
