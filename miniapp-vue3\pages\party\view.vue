<template>
	<view>
		<my-page-header :title="model.name" :showBack="true"></my-page-header>

		<view class="create-panle">
			<!-- 			<view class="g-center">
				<text>{{model.name}}</text>
				<text>[ {{model.province}} - {{model.city}} ]</text>
			</view> -->
			<view class="flex-gap10 mpt10 flex-center-all">
				<view class="iconfont icon-calendar f-lgx2"></view>
				<view class="f-color-success f-lgx2">
					{{(""+model.beginDate).substring(0,10)}}
				</view>
				<view class="f-color-success f-lg f-border">
					:{{(""+model.beginDate).substring(11,5)}}
				</view>
			</view>
			<view class="f-lg mpt10  g-center" style="max-width: 90%;margin-left:auto;margin-right: auto;">
				{{model.address}}
			</view>

			<view class="card mpt10 pd10">

				<view class="g-gray mpt10 mpb10">
					{{model.content||'暂无活动描述'}}
				</view>
				<view class="flex flex-between flex-center f-lg">
					<view class="flex flex-gap5 flex-center" v-if="model.contacter">
						<my-user-avatar :data="model.user" size="sm" allowLink></my-user-avatar>
						{{model.contacter}}
					</view>
					<view class="flex flex-gap5" v-if="model.contacterPhone" @tap="callPhone(model.contacterPhone)">
						<view class="iconfont icon-phone" style="vertical-align: initial;"></view>
						{{model.contacterPhone}}
					</view>
				</view>
			</view>

			<!--功能按钮-->
			<view class="btn-bar">
				<button class="my-btn " @tap="goLocation(model)">
					<view class="iconfont icon-nav f-color-success"></view>
					<view class="f-time">导航</view>
				</button>

				<button class="my-btn " @tap="like(model)">
					<view class="iconfont icon-like" :class="{'f-color-danger':model.isLike}"></view>
					<view class="f-time">点赞 {{model.static.likeCount || ''}}</view>
				</button>

				<button class="my-btn " @tap="interestClick">
					<view class="iconfont icon-love" :class="{'f-color-danger':model.isInterest}"></view>
					<view class="f-time">预报名 {{model.static.interestCount || ''}} </view>

				</button>
				<button class="my-btn " open-type="share">
					<view class="iconfont icon-share f-color-success"></view>
					<view class="f-time">分享</view>
				</button>
			</view>
			
			<map class="map" id="map"
				:longitude="model.lon" 
				:latitude="model.lat"
				:markers="[{
					id:1,
					width:20,
					height:30,
					latitude:model.lat,
					longitude:model.lon,
					name:model.name
				}]"
				scale="15"
				:enable-scroll="false" 
				:enable-zoom="false">
			</map>

			<view class="pd10">
				<my-cell label="活动类型" line="2" :right="app.tryEnum('partyType',model.type)"></my-cell>
				<my-cell label="活动时长" line="2" :right="app.tryEnum('partyTime',model.time)"></my-cell>
				<my-cell label="活动规模" line="2" :right="app.tryEnum('partySize',model.size)"></my-cell>
				<my-cell label="场地类型" line="2" :right="app.tryEnum('venueType',model.venueType)"></my-cell>
				<my-cell label="附近停车" line="2" :right="app.tryEnum('venueParking',model.parking)"></my-cell>
				<my-cell label="是否收费" line="2" :right="app.tryEnum('partyPaid',model.paid)"></my-cell>
				
			</view>



			<view class="my-box" v-if="model.paidIntro">
				<view style="margin:10rpx 0;">
					{{model.paidIntro}}
				</view>
			</view>
		</view>
		<!--发布者-->
		<view class="pd20 g-center">
			<text class="f-sm ml">{{(""+model.updatedAt).substring(0,11)}} 发布</text>
		</view>
		<!--编辑-->
		<view class="page-bottom">
			<navigator v-if="user.account.type>900 || model.isSelf" class="my-btn success"
				:url="`/edit?id=${model.id}`">编辑</navigator>
		</view>

		

		
		<uni-popup ref="confirmJoinRef" type="center">
			<view class="card" style="width: 80%;margin: auto;padding: 30upx;">
				<view class="pd20">
					如果您对此活动感兴趣，可以通过预报名功能表示您有意参加，这是非强制的，仅表示意向。
				</view>
				<view class="flex flex-between">
					<button class="my-btn light" style="width: 160upx;" @tap="confirmJoinRef.close()">取消</button>
					<button class="my-btn success" style="width: 160upx;" @tap="interest">确认</button>
				</view>
			</view>
		</uni-popup>
		
		
		
		<uniPopup type="center" ref="likesPopRef" :animation="false">
			<view style="height: 70vh;width: 80vw;background: rgba(255,255,255,1);padding: 20rpx;">
				<view class="flex flex-between">
					<view class="my-title">
						点赞记录
					</view>
					<view class="">
						<button class="my-btn" @tap="interest">取消</button>
					</view>
				</view>

				<my-list-async 
				:transf="parseItemTime" 
				:pageSize="15" 
				:autoLoad="false" 
				style="height: 100%;"
				:url="`/party/${model.id}/likes`" ref="likesPopMlRef">
					<template #item="{item}">
						<view class="flex flex-between line-2 mp10">
							<my-user-avatar :data="item.user" size="sm" allowLink />
							<view style="padding-right: 20rpx;">
								{{ item.user.nickName }}
							</view>
							<view class="f-sm">{{item.updatedAt}}
							</view>
		
						</view>
					</template>
		
				</my-list-async>
			</view>
		</uniPopup>
		
		<uniPopup type="center" ref="interestPopRef" :animation="false">
			<view class="card" style="height: 70vh;width: 80vw;padding: 20rpx;">
				<view class="flex flex-between">
					<view class="my-title">
						预报名记录
					</view>
					<view class="">
						<button class="my-btn light sm" @tap="interest">取消</button>
					</view>
				</view>
				<my-list-async :transf="parseItemTime" 
				:pageSize="15" 
				:autoLoad="false" 
				style="height: 100%;"
					:url="`/party/${model.id}/interests`" ref="interestPopMlRef">
					<template #item="{item}">
						<view class="flex flex-between flex-center line-2 mp10">
							<view class="flex flex-gap5 flex-center">
								<my-user-avatar 
								:data="item.user" 
								size="mini"
								showName
								isLineLayout
								allowLink />
							</view>

							<view class="f-sm">{{item.updatedAt}}</view>
						</view>
					</template>
				</my-list-async>
			</view>
		</uniPopup>
	</view>

</template>

<script setup lang="ts">
	import { ref, reactive, computed } from 'vue'
	import { useStore } from 'vuex'
	import { onLoad, onShareAppMessage, onShareTimeline } from '@dcloudio/uni-app'
	import { formatDate, getLocByLL } from '@/common/funs'
	import { appStore } from '../../store'
	import { userStore } from '../../store/user'
	import ajax from '../../common/ajax'

	const likesPopRef = ref();
	const likesPopMlRef = ref();
	const interestPopRef = ref()
	const interestPopMlRef = ref()
	const confirmJoinRef = ref();
	
	const app = appStore()
	const user = userStore();
	console.log(user.tryLocal.lat,user.tryLocal.lon)
	const model = reactive<any>({
		name: '',
		address: '',
		lat: '',
		lon: '',
		VenueId: '',
		city: '',
		province: '',
		beginDate: '',
		type: 0,
		size: 0,
		time: 0,
		paid: 0,
		parking: 1,
		venueType: 1,
		isSelf: false,
		static: {},
		content: '',
		paidIntro: '',
		createdAt:'',
		updatedAt:'',
		user:{},
	})

	function dateFormat(val : string, fmt : string) {
		return formatDate(new Date(val), fmt)
	}

	onLoad((query : any) => {
		if (query.id) {
			model.id = query.id
			load(query.id)
		}
	})

	onShareTimeline(() => {
		return {
			title: `约毽:${model.name}`,
			query: `id=${model.id}`
		}
	})
	onShareAppMessage(() => {
		return {
			title: `约毽:${model.name}`,
			path: '/pages/Party/PartyView?id=' + model.id
		}
	})

	async function load(id : string) {
		const res = await ajax.get<any>('/party/' + id);
		
		res.images = res.images ? res.images.split(',').map((v : string) => ({ url: v, label: '' })) : []
		if (res.lon) {
			res.lon = parseFloat(res.lon)
			res.lat = parseFloat(res.lat)
		}
		res.isSelf = res.userId == user.info.id;
		Object.assign(model, res)
	}



	function parseItemTime(item : any) {
		item.updatedAt = formatDate(item.updatedAt, 'yyyy/MM/dd hh:mm')
		return item
	}

	function goLocation(item : any) {
		wx.openLocation({
			latitude: item.lat,
			longitude: item.lon,
			scale: 15,
			name: item.name
		})
	}

	function callPhone(phone : string) {
		wx.makePhoneCall({ phoneNumber: phone })
	}

	async function chooseLocation() {
		const res= await uni.chooseLocation({
			latitude: model.lat,
			longitude: model.lon
		})
		if (!res.errMsg) {
			model.lat = res.latitude
			model.lon = res.longitude
			model.address = res.address
			model.name = res.name
			const { city, province } = await getLocByLL(model.lat, model.lon)
			changedCity([province, city])
		}
	}

	function changedCity(v : [string, string]) {
		model.province = v[0]
		model.city = v[1]
	}

	async function save() {
		let error = ''
		if (!model.name && !model.contacterPhone) {
			error = '活动名不填，电话也不留，您这是在对暗号的特工行动？'
		}
		if (!model.address || !model.lon || !model.city) {
			error = '场地位置不能为空'
		}
		if (error) {
			uni.showToast({ icon: 'none', title: error })
			return
		}
		//await store.state.$funs.wxContentCheck(model.name + ',' + (model.intro || ''))
		const postData = { ...model }
		delete postData.user
		delete postData.partyStatic
		await ajax.post('/party', postData)
		
		uni.navigateBack();
	}

	async function like(modelObj : any) {
		await ajax.post(`/party/${modelObj.id}/like`)
		model.isLike = !model.isLike
		if (!model.static) model.static = {}
		model.static.likeCount = (model.static.likeCount || 0) + (model.isLike ? 1 : -1)
	}

	function interestClick() {
		if (model.isInterest) {
			interestPopRef.value.open();
			interestPopMlRef.value.load();
			
		} else {
			confirmJoinRef.value.open()
			
		}
	}
	//post join
	async function interest() {
		await ajax.post(`/party/${model.id}/interest`)
		model.isInterest = !model.isInterest
		if (!model.static) model.static = {}
		model.static.interestCount = (model.static.interestCount || 0) + (model.isInterest ? 1 : -1)
		// @ts-ignore
		confirmJoinRef.value.close()
		if(!model.isInterest){
			interestPopRef.value.close()
		}
	}

	// refs 用于 popup 控制
	const refs = {} as any
</script>

<style lang="scss">
	.map {
		box-shadow: 0 0 15upx rgba(0, 0, 0, .2);
		border: 2px solid #fff;
		border-radius: 6px;
		box-sizing: border-box;
		height: 120px;
		width: 100%;
		margin: 10px 0;
		
	}

	.line-1 {}

	.create-panle {
		padding: 20rpx;
		input {
			color: #fff;
		}

		.icon-arrow {
			color: #dbdbdb59 !important;
		}

		.my-cell,
		my-cell {
			&.right {
				color: #fff;
			}
		}
	}

	.uni-datetime-picker-btn {
		color: #4d4d88 !important;
	}

	.uni-datetime-picker-popup {
		color: #333 !important;
	}

	.btn-bar {
		display: flex;
		align-items: center;
		color: #fff;
		position: relative;
		justify-content: space-between;
		gap: 10px;

		view>button {
			flex: 1
		}

		.iconfont {
			display: block;
			font-size: 1.5em;
		}

		&>button {
			flex-grow: 0;
			flex-shrink: 0;
			flex: 1;
			line-height: 1;
			padding-bottom: 10rpx;
		}
	}


	.my-card-light {
		background: linear-gradient(180deg, #ecc01fd1, #e07c00ed 100%);
	}
	.f-border{
		border: 2px solid rgba($uni-text-color,.8);
		padding: 1px 4px;
		border-radius: 5px;
	}
</style>