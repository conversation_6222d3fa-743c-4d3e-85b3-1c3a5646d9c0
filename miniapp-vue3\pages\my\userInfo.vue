<template>
	<view class="container" :style="{'background-image':'url('+($asset('bg')||'')+')',height:'100vh'}">
		<view style="padding: 30px; text-align: center;">
			<myAvatar refresh showName></myAvatar>
			<view class="f-sm">
				(点击头像更新)
			</view>
		</view>
		<view class="my-card-light">
			<myCell label="姓名" :right="user.info.name" line="1" rightIcon="iconfont iconedit1  f-color-main hover" @rightClick="updateUserPop('name', user.info.name, '修改我的真实姓名')"></myCell>
			<myCell label="昵称" :right="user.info.nickName" line="1"
				rightIcon="iconfont iconedit1  f-color-main hover"
				@rightClick="updateUserPop('nickName', user.info.nickName, '修改我的昵称', '文字长度小于30个', 'nickname')"
			></myCell>
			<myCell label="性别" :right="user.info.sex == 0 ? '未知' : user.info.sex == 1 ? '男' : '女'" line="1"></myCell>
			<myCell label="签名" :right="user.info.sign" line="1" rightIcon="iconfont iconedit1  f-color-main hover" @rightClick="updateUserPop('sign', user.info.sign, '修改我的签名', '签名文字长度小于30个')"></myCell>
			<myCell label="手机号码" :right="user.info.phone" right="功能开发中"></myCell>
		</view>
		<inputPopup ref="editPop" v-model="d.model.value" :title="d.model.title" :placeholder="d.model.placeholder" @ok="updateUserSave"></inputPopup>
	</view>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { userStore } from '../../store/user'

const user = userStore()
const editPop = ref()

const d = reactive({
	loginCode: '',
	model: {
		field: '',
		value: '',
		title: '',
		placeholder: ''
	}
})

onLoad(() => {
	wx.login({
		success: (login: any) => {
			d.loginCode = login.code
		}
	})
})

async function syncPhone(e: any) {
	e.detail.loginCode = d.loginCode
	await user.syncPhone(e)
	wx.login({
		success: (login: any) => {
			d.loginCode = login.code
		}
	})
}

async function updateUserPop(field: string, value: string, title: string, placeholder?: string, type?: string) {
	d.model.field = field
	d.model.value = value
	d.model.title = title
	d.model.placeholder = placeholder || ''
	editPop.value.open(type)
}

async function updateUserSave() {
	const postDate = { [d.model.field]: d.model.value }
	await user.updateUser(postDate)
	editPop.value.close()
}
</script>

<style lang="scss">

</style>
