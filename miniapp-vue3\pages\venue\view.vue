<template>
	<view class="page-bottom-fix">
		<my-page-header :title="d.model.name" :showBack="true"></my-page-header>
		<view class="container">

			<view style="text-align:center;;line-height:1.8;">
				<my-rate :readonly="true" style="display: inline-block;" :value="d.model.star" :max="5"
					color="rgba(255,255,255,.5)" active-color="#ff6a00" :size="20" />
			</view>

			<!--轮播图-->
			<view v-if="d.model.images && d.model.images.length!=0" class="shaow swiper mpb10 mpt10" style="">
				<swiper class="swiper-box" style="height: 100%; width: 100%;" :indicator-dots="true">
					<swiper-item v-for="(item,idx) in d.model.images" :key="idx">
						<image class="swiper-item banners" :src="imgUtil.reSize(item,'lg')" mode="aspectFill"
							style="width: 100%;height: 460rpx;" @tap="viewImage(d.model.images,idx)"></image>
					</swiper-item>
				</swiper>

			</view>
			<view class="mp10 flex flex-gap10">
				<view class="iconfont icon-location f-lg"></view>
				{{d.model.address}}
			</view>

			<view class="my-box" >
				<view class="my-tag" v-if="d.model.size">
					<label>规模:</label>
					{{app.tryEnum('venueSize',d.model.size)}}
				</view>
				<view class="my-tag" v-if="d.model.type">
					<label>场地:</label>
					{{app.tryEnum('venueType',d.model.type)}}
				</view>
				<view class="my-tag" v-if="d.model.parking">
					<label>车位:</label>
					{{app.tryEnum('venueParking',d.model.parking)}}
				</view>
				<view class="my-tag" v-if="d.model.able">
					<label>使用:</label>
					{{app.tryEnum('venueAble',d.model.able)}}
				</view>
				<view class="my-tag" v-if="d.model.paid">
					<label>费用:</label>
					{{app.tryEnum('venuePaid',d.model.paid)}}
				</view>
				<view v-if="d.model.paidIntro" style="margin-top:10rpx" class="f-sm f-color-gray">
					{{d.model.paidIntro || ""}}
				</view>
			</view>
			

			<view class="mp10">
				{{d.model.intro||'暂无相关描述'}}
			</view>
			<view class="flex">
				<view class="" v-if="d.model.contacter">
					<view class="iconfont iconplayer"></view>
					{{d.model.contacter}}
				</view>
				<view v-if="d.model.contacterPhone" class="" @tap="callPhone(d.model.contacterPhone)">
					<view class="iconfont icondianhua"></view>
					{{d.model.contacterPhone}}
				</view>
			</view>


			<!--功能按钮-->
			<view class="btn-bar">

				<button class="my-btn " @tap="goLocation(d.model)">
					<view class="iconfont icon-nav f-color-success"></view>
					<view class="f-time">导航</view>
				</button>

				<button class="my-btn " @tap="like(d.model)">
					<view class="iconfont icon-like warn" :class="{'yes':d.model.isLike}"></view>
					<view class="f-time">{{d.model.static.likeCount || ''}} 喜欢</view>
				</button>

				<button class="my-btn " @tap="visit(d.model)">
					<view class="iconfont icon-hot f-color-warn"></view>
					<view class="f-time">{{d.model.static.visitCount || ''}} 签到</view>

				</button>
				<button class="my-btn " open-type="share">
					<view class="iconfont icon-share f-color-success"></view>
					<view class="f-time">分享</view>
				</button>
				<!-- 					<button class="my-btn white" >
						<view class="iconfont iconicon_share" ></view>朋友圈
					</button>	 -->

			</view>


			<!--地图-->
			<view class="shaow mpt10 mpb10">
				<map class="map" id="map" 
				:longitude="d.model.lon" 
				:latitude="d.model.lat"
				:enable-scroll="false"
				:enable-zoom="false"
				:markers="[{id:1,width:20,height:30,latitude:d.model.lat,longitude:d.model.lon,name:d.model.name}]"
				scale="15" style="width: 100%; height:120px;">
				<cover-view style='width:100%;height:100%'></cover-view>
				</map>
			</view>

			<!--贡献者-->
			<view class="center flex-center-all mp10 flex-gap10" v-if="d.model.user?.id" >
				<text>致谢</text>
				<my-user-avatar size="mini" :data="d.model.user" isLineLayout sh showName
					@tap="goUser(d.model.user.id)" />
				<text class="">{{(d.model.createdAt+"").substring(0,11)}} 提供</text>
			</view>

			<!--留言-->
			<my-comment-top :targetId="d.id" v-if="app.sys.allowComment == 1" />

			<view class="page-bottom" >
				<view v-if="user.isAdmin || app.sys.allowPushVenueByUser==1" class="my-btn"
						@click="showHistory">查看变更</view>
				
				<navigator v-if="user.isAdmin || app.sys.allowPushVenueByUser==1" class="my-btn success"
					:url="`edit?id=${d.id}`">重新编辑</navigator>
</view>

			<!--最近签到-->
			<uni-popup type="center" ref="visitPop" :animation="false">
				<view style="height: 70vh;width: 80vw;background: rgba(255,255,255,1);padding: 20rpx;">
				<my-list-async 
					:transf="parseItemTime" 
					:pageSize="15" 
					:autoLoad="false" 
					style="height: 100%;" 
					:url="`/venue/${d.model.id}/visits`" 
					ref="visitPopMl">
					<template #item="{ item, _index, scope }">
							<view class="flex line-2" >
								<view class="flex">
									<my-user-avatar :data="item.user" allowLink showName isLineLayout></my-user-avatar>
								</view>
								<view class="comment-content">{{ item.content }}</view>
							</view>
						
					</template>
				</my-list-async>
				</view>
			</uni-popup>
			<!--变更历史-->
			<uni-popup type="center" ref="historyPopRef" :animation="false">
				<view class="my-popup">
					<my-list-async 
					:transf="parseUpdateHistory" 
					:pageSize="15" 
					:autoLoad="false" 
					style="height: 100%;"
					:url="`/venue/${d.model.id}/updateHistory`" 
					ref="historyPopMlRef">
						<template #item="{item, _index, scope}">
							<view class="line-2" >
								<view class="flex flex-between">
									<view class="">
										<image class="avatar-icon sm" style="margin-right: 10rpx;" :src="item.user.avatar"
											@tap="goUser(item.user.id)" />
											<text style="padding-right: 20rpx;">{{ item.user.nickName }}</text>
									</view>
									<view class="f-time">{{ item.updatedAt }}</view>
								</view>
								<view class="history-content">
									<view class="" v-for="his in item.content">
										{{his.field}}:( {{his.old}} ) ➡ ( {{his.new}} )
									</view>
								</view>
							</view>
						</template>
					</my-list-async>
				</view>
			</uni-popup>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import { reactive, ref } from 'vue'
	import { userStore } from '../../store/user'
	import { appStore } from '../../store';
	import { onLoad, onShareTimeline, onShareAppMessage } from "@dcloudio/uni-app"
	import { formatDate, imgUtil } from '../../common/funs';

	import ajax from '../../common/ajax';
	const user = userStore();
	const app = appStore();
	const historyPopRef = ref();
	const historyPopMlRef = ref();
	const visitPopRef = ref();
	const visitPopMlRef = ref();
	const x = ref([])
	const d = reactive({
		id:'',
		model: {
			id: '',
			name: '',
			address: '',
			lat: '',
			lon: '',
			size: '',
			paid: '',
			images: '',
			imagesArr: [],
			able: "",
			type: '',
			parking: '',
			contacter: '',
			contacterPhone: '',
			createdAt: '',
			updatedAt: '',
			star: 0,
			isLike: false,
			user: {
				nickname: '',
				avatar: '',
				id: ''
			},
			static: {
				"visitCount": 0,
				"hotCount": 0,
				"likeCount": 0,
				"shareCount": 0,
			},
		} as any,
		isVisited: false,
		comments: [],
		commentModel: {
			content: ''
		},
	}
	);

	onLoad(({ id } : any) => {

		if (id) {
			d.id = id;
			load(id)
		}
		wx.showShareMenu({
			withShareTicket: true,
			menus: ['shareAppMessage', 'shareTimeline']
		})
	})

	//onMenuShareTimeline
	onShareTimeline(() => {
		return {
			title: `毽球场地:${d.model.name}`,
			query: `id=` + d.model.id,
		}
	})
	d
	onShareAppMessage(() => {
		return {
			title: `毽球场地:${d.model.name}`,
			path: '/pages/Venue/View?id=' + d.model.id
		}
	})

	function goUser(id : string) {
		uni.navigateTo({
			url: "/pages/User/User?id=" + id
		})
	}
	function callPhone(phone : string) {
		wx.makePhoneCall({
			phoneNumber: phone
		})
	}
	function goLocation(item : IVenue) {
		wx.openLocation({
			latitude: parseFloat(item.lat),
			longitude: parseFloat(item.lon),
			scale: 15,
			name: item.name,
		})
	}
	function viewImage(urls : string[], idx = 0) {
		wx.previewImage({
			urls,
			current: urls[idx]
		})
	}
	function parseUpdateHistory(item : any) {
		item.content = JSON.parse(item.content);
		item.updatedAt = formatDate(item.updatedAt, 'yyyy/MM/dd hh:mm');
		return item;
	}
	function parseItemTime(item : IVenue) {
		item.updatedAt = formatDate(item.updatedAt, 'yyyy/MM/dd hh:mm')
		return item;
	}
	async function visit(model : IVenue) {
		if (!d.isVisited) {
			let res = await ajax.post(`/venue/${model.id}/visit`, {
				lon: user.currentLocal.lon,
				lat: user.currentLocal.lat
			})
			if (res === 0) {
				uni.showToast({
					title: '您已签到',
					icon: "none"
				})
			} else {
				d.model.static.visitCount++;
				uni.showToast({
					title: '签到成功'
				})
			}
			d.isVisited = true;

		}
		visitPopRef.value.open();
		visitPopMlRef.value.load();
		// uni.showModal({
		// 	title:'是否分享?',
		// 	success(res) {
		// 		if(res.confirm){
		// 			uni.share({

		// 			})
		// 		}
		// 	}
		// })
	}
	function showHistory() {
		historyPopRef.value.open();
		historyPopMlRef.value.load();
	}
	async function like(model : IVenue) {
		let res = await ajax.post<any>(`/venue/${d.model.id}/like`)
		d.model.isLike = res.result;
		d.model.static.likeCount = res.count
	}

	async function load(id : string) {

		let res = await ajax.get<any>('/venue/' + id)
		res.images = res.images ? res.images.split(',') : []
		if (res.lon) {
			res.lon = parseFloat(res.lon)
			res.lat = parseFloat(res.lat)
		}
		d.model = res;

	}
</script>

<style lang="scss" scoped>
	.swiper {
		height: 180px;
		width: 100%;
		border-radius: 4px;
		overflow: hidden;
	}
.my-box{
	display: flex;
	flex-wrap: wrap;
	gap: 6px;
	margin: 15px 0;
	.my-tag {
		width: 112px;
	}
}


	.tab-item {
		padding: 10rpx 20rpx;
		color: #fff;
		opacity: .8;

		&.active {
			background: #fff;
			color: #ff6a00;
			opacity: 1;
		}
	}

	.map {

		padding: 10upx;
		box-sizing: border-box;
		background-color: #fff;
	}

	input {
		color: #fff;
	}

	.shaow {
		box-shadow: 0 0 15upx rgba(0, 0, 0, .2);
	}

	.likeIcon {
		color: #fff;
		text-shadow: 0 0 4rpx $uni-color-warning, 0 0 4rpx $uni-color-warning, 0 0 4rpx $uni-color-warning;

		&.yes {
			color: $uni-color-warning;
			text-shadow: none;
		}
	}

	.btn-bar {
		display: flex;
		align-items: center;
		color: #fff;
		justify-content: space-between;
		gap: 10px;

		.iconfont {
			font-size: 1.5em;
		}

		button {
			flex-grow: 0;
			flex-shrink: 0;
			flex: 1;
			line-height: 1;
		}
	}

	.image-null {
		height: 100%;
		background: rgba(0, 0, 0, 0.1);
		text-align: center;
		line-height: 460rpx;
		color: rgba(0, 0, 0, 0.4);

	}

	.history-content {
		text-align: left;
		padding-left: 60rpx;
		color: dimgray;
	}
</style>