<template>
	<view class="body" style="height: 100vh;" :style="{'background-image':'url('+($asset('bg') || '')+')'}">
		<view style="height: 100%" class="container">
			<myListAsync
				ref="ml"
				:params="d.searchParams"
				url="/my/venues"
				:autoLoad="false"
				:pageSize="12"
				:transf="parseImages"
			>
				<view class="my-card item" slot="item" slot-scope="{item,_index,scope}" @tap="goView(item)">
					<image :src="item.Venue.images[0] + '?imageView2/1/w/220/h/220'" class="headerImg" mode="aspectFill"></image>
					<view style="line-height: 2;padding: 0 20rpx;">
						<view class="">
							{{ item.Venue.name }}
						</view>
						<view class="f-sm f-color-gray-light">
							{{ item.Venue.address }}
						</view>
					</view>
				</view>
			</myListAsync>
		</view>
	</view>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted } from 'vue'

const ml = ref()

const d = reactive({
	searchParams: {}
})

onMounted(() => {
	ml.value.tryFirstLoad()
})

function parseImages(item: any) {
	item.Venue.images = (item.Venue.images || "").split(',')
	return item
}

function goView(item: any) {
	uni.navigateTo({
		url: `/pages/Venue/View?id=${item.Venue.id}`
	})
}
</script>

<style lang="scss">
	.item{
		display: flex;
		padding: 0rpx 0;
		.headerImg{
			width: 240rpx;
			height: 240rpx;
			flex-grow: 0;
			flex-shrink: 0;
			outline: 4px solid rgba(0, 0, 0, 0.09019607843137255);
		}
	}
</style>
