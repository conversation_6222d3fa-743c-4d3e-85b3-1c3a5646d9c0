import { tokenStore<PERSON>ey } from "../store/user";

/**
 * ajax
 * by windbell2
 */

export const API_HOST = "http://localhost:3000/api"
//export const API_HOST = "https://toolapi.falan.work/wx/"

let requestCount = 0;
function getRequestHeader() {
	return {
		//'content-type': 'application/json', //自定义请求头信息
		'_s':'jianqiu',
		'token': (uni.getStorageSync(tokenStoreKey)||{token:""}).token
	}
}

const ajax = async (url : string, params = {}, method : any, options : any = {}) => {

	if (options && options.showLoading) {
		requestCount++;
		uni.showLoading({
			title: '加载中...'
		});
	}

	/** mock **/
	// console.log("ajax = > " + method +" "+url)
	// console.log(params)
	// console.log(JSON.stringify(params))
	let res : any;
	try {
		let requestUrl : string = /^http/.test(url) ? url : API_HOST + url
		res = await uni.request({
			url: requestUrl,
			data: params,
			method,
			header: getRequestHeader(),
		});
	} catch (e) {
		if (!options.hidden) {
			uni.showToast({
				icon: 'none',
				title: JSON.stringify(e)
			})
		}
		console.log('44444444')
		return Promise.reject(new Error("NET"));
	} finally {
		if (options && options.showLoading) {
			requestCount--;
			if (!requestCount) {
				uni.hideLoading();
			}
		}
	}
	if (res.statusCode == 200 || res.statusCode==201) {
		if (url.indexOf('.json') != -1) { // json文件
			return Promise.resolve(res.data);
		} else if (res.data.content && typeof (res.data.content) == "string") { //旧接口
			return Promise.resolve(res.data.content);
		} else if (res.data.code !== undefined) {
			if (res.data.code == 0) {
				return Promise.resolve(res.data.data);
			} else {
				let err = res.data.msg || res.data.message;
				if (!options.hidden) {
					uni.showToast({
						icon: "none",
						title:url+" => "+ err
					})
				}
				return Promise.reject({message:err});
			}

		} else {
			return Promise.resolve(res.data);
		}
	}
	else {
		if (!options.hidden) {
			uni.showToast({
				icon: 'none',
				title: res.statusCode.toString(),
			})
		}
		return Promise.reject(new Error("NET"));
	}
};

export default {
	get<T extends Object>(url : string, data ?: any, options ?: any) : Promise<T> {
		return ajax(url, data, 'GET', options)
	},

	post<T extends Object>(url : string, data ?: any, options ?: any) : Promise<T> {
		return ajax(url, data, 'POST', options)
	},
	put(url : string, data ?: any, options ?: any) {
		return ajax(url, data, 'PUT', options)
	},
	del(url : string, data ?: any, options ?: any) {
		return ajax(url, data, 'DELETE', options)
	},
	getRequestHeader,
	upload(url : string, filePath : string) {
		requestCount++;
		uni.showLoading({
			title: '加载中...'
		});
		return new Promise(async (r, j) => {
			let params = {
				name: 'file',
				url: API_HOST + url,
				filePath,
				header: {
					'content-type': 'application/json', //自定义请求头信息
					...getRequestHeader()
				},
			}

			uni.uploadFile({
				...params,
				success: (res) => {
					console.log('success upload=>')
					console.log(res)
					requestCount--;
					if (!requestCount) {
						uni.hideLoading();
					}
					try {
						let resJson = JSON.parse(res.data);
						if (resJson.code == 0) {
							r(resJson.data)
						} else {
							j(resJson.data.message)
						}
					} catch (e) {
						j(res)
					}

				},
				fail: (err) => {
					requestCount--;
					if (!requestCount) {
						uni.hideLoading();
					}
					j(err.errMsg)
				}
			})
		})

	}
}