<template>
	
	<view >
		<my-page-header :title="model.name" :showBack="true"></my-page-header>

		<view class="create-panle">
			<my-cell label="活动城市" labelCls="label-require" line="2">
				<template #rightSlot>
					<my-select-city v-model="model.divisionCode" />
				</template>
			</my-cell>

 			<my-cell label="活动标题" labelCls="label-require" line="2">
				<template #rightSlot>
					<input class="my-input" placeholder="点击输入🖋" type="text" v-model="model.name" :adjust-position="true" />
				</template>

			</my-cell>
			<my-cell label="活动时间" labelCls="label-require" line="2">
				<template #rightSlot>
					<uni-datetime-picker 
						type="datetime"
						:value="model.beginDate"
						:start="beginDate"
						return-type="string"
						@change="ddChange"
					>
						<text v-if="model.beginDate">{{model.beginDate.substring(0,16)}}</text>
						<text class="f-color-warn" v-else>选择时间</text>
					</uni-datetime-picker>
				</template>
			</my-cell>
			<my-cell label="场地选择" labelCls="label-require" url="select-venue"  arrow=1 line="2"></my-cell>
				
				<map class="map" id="map" :longitude="model.lon" :latitude="model.lat" :markers="[{id:1,width:20,height:30,latitude:model.lat,longitude:model.lon,name:model.name}]" scale="15" style="width: 100%; height: 160px;" @tap="chooseLocation"></map>
			<my-cell label="场地地址" labelCls="label-require" @click="chooseLocation" line="2">
				<template #rightSlot>
					<input style="width: 260px;font-size: .9em;" placeholder="点击输入🖋" class="my-input"  type="text" v-model="model.address" />
				</template>

			</my-cell>

<!-- 			<view class="mt"></view>
			<my-cell label="详细地址"  line="2">
				<input slot="rightSlot" placeholder="场地的详细地址" type="text" v-model="model.address" />
			</my-cell> -->

			<my-cell label="活动类型" line="2">
				<template #rightSlot>
					<my-switch cls="sm":d="enums.partyType" v-model="model.type"></my-switch>
				</template>
				
			</my-cell>
			<my-cell label="活动介绍" ></my-cell>
			<textarea v-model="model.content" class="my-textarea " style="height: 80px;padding: 20rpx;"></textarea>
			<my-cell label="联系之人" labelCls="label-require" line="2">
				<template #rightSlot>
					<input placeholder="点击输入🖋" class="my-input"  type="text" v-model="model.contacter" />
				</template>
				
			</my-cell>
			<my-cell label="联系方式" labelCls="label-require" line="2">
				<template #rightSlot>
					<input placeholder="点击输入🖋" class="my-input"  type="text" v-model="model.contacterPhone" />
				</template>
				
			</my-cell>
			
			
			<view class="mpt10">
				<view class="g-right" @click="showMore = !showMore">
					<view class="my-tag border warn">
						更多选项 >
					</view>
				</view>
				<view v-show="showMore">

					<my-cell label="活动时长" line="2" >
						<template #rightSlot>
							<my-switch cls="sm warn" :d="enums.partyTime" v-model="model.time"></my-switch>
						</template>
					</my-cell>
					<my-cell label="活动规模" line="2" >
						<template #rightSlot>
							<my-switch cls="sm warn" :d="enums.partySize" v-model="model.size"></my-switch>
						</template>
						
					</my-cell>

					<my-cell label="场地类型" line="2" >

						<template #rightSlot>
							<my-switch cls="sm warn" :d="enums.venueType" v-model="model.venueType"></my-switch>
						</template>
					</my-cell>
					<my-cell label="附近停车" line="2" >

						<template #rightSlot>
							<my-switch cls="sm warn" :d="enums.venueParking" v-model="model.parking"></my-switch>
						</template>
					</my-cell>

					<my-cell label="是否收费" line="2">

						<template #rightSlot>
							<my-switch cls="sm warn" :d="enums.partyPaid" v-model="model.paid"></my-switch>
						</template>
					</my-cell>
					<my-cell label="收费补充" ></my-cell>
					<textarea class="my-textarea light" 
						placeholder="无费用可不填" 
						v-model="model.paidIntro" style="height: 60px"></textarea>
					<view class="" style="height: 80px;">
						
					</view>
				</view>
			</view>
		</view>
		<view class="page-bottom" >
			
			<button class="my-btn gray" @click="del" v-if="model.id && user.isAdmin">删除</button>
			
			<button class="my-btn success" @click="save">保存</button>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import uploadImages from '@/components/upload-images.vue'
import { formatDate, getLocByLL } from '@/common/funs'
import ajax from '../../common/ajax'
import { appStore } from '../../store'
import { userStore } from '../../store/user'

// 1. store
const app = appStore()
const enums = app.enums
const user =userStore();
// 2. model
const model = reactive({
  id: '',
  name: '',
  address: '',
  lat: 0,
  lon: 0,
  venueId: '',
  city: '',
  province: '',
  divisionCode:0,
  beginDate: '',
  type: 0,
  size: 0,
  time: 0,
  paid: 0,
  parking: 1,
  venueType: 1,
  contacter: '',
  contacterPhone: '',
  content: '',
  paidIntro: '',
  user:{},
  static:{}
})
const venue = ref<any>({})
const showMore = ref(false)
const beginDate = ref(formatDate(new Date(), 'yyyy-MM-dd'))

// 3. 地址显示
const address = computed(() => model.address)

// 4. 生命周期
onLoad((query: any) => {
  if (query.id) {
    model.id = query.id
    load(query.id)
  } else {
    setDefaultLocation()
  }
})

onMounted(() => {
  uni.$on('selectVenue', setVenue)
})
onUnmounted(() => {
  uni.$off('selectVenue', setVenue)
})

// 5. 方法
function dateFormat(val: string, fmt: string) {
  return formatDate(new Date(val), fmt)
}

function ddChange(v: string) {
  model.beginDate = formatDate(new Date(v), 'yyyy-MM-dd hh:mm')
}

async function load(id: string) {
  const res = await ajax.get<any>('/party/' + id)
  res.images = res.images ? res.images.split(',').map((v: string) => ({ url: v, label: '' })) : []
  if (res.lon) {
    res.lon = parseFloat(res.lon)
    res.lat = parseFloat(res.lat)
  }
  Object.assign(model, res)
}

function setVenue(venueObj: any) {
  model.venueId = venueObj.id
  venue.value = venueObj
  model.name = model.name || `#${venueObj.name}#`
  model.lat = venueObj.lat
  model.lon = venueObj.lon
  model.address = venueObj.address
  model.parking = venueObj.parking
  model.city = venueObj.city
  model.province = venueObj.province
  model.venueType = venueObj.type || venueObj.buildType
}

async function setDefaultLocation() {
  model.lat = user.currentLocal.lat!
  model.lon = user.currentLocal.lon!
}

async function chooseLocation() {
  const res = await uni.chooseLocation({
    latitude: model.lat,
    longitude: model.lon
  })
  if (!res.errMsg) {
    model.venueId = ''
    model.lat = res.latitude
    model.lon = res.longitude
    model.address = res.address
    model.name = res.name
    const { city, province } = await getLocByLL(model.lat, model.lon)
    changedCity([province, city])
  }
}

function changedCity(v: [string, string]) {
  model.province = v[0]
  model.city = v[1]
}

async function save() {
  let error = ''
  if (!model.name) error = '活动名称不为空'
  if (!model.contacter && !model.contacterPhone) error = '联系人，联系方式总要留一个吧？'
  if (!model.address || !model.lon || !model.city) error = '活动场地位置不能为空'
  if (!model.beginDate) error = '时间不能为空'
  if (error) {
    uni.showToast({ icon: 'none', title: error })
    return
  }
  //await store.state.$funs.wxContentCheck(model.name + ',' + (model.content || ''))
  const postData = { ...model } as any;
  delete postData.user
  delete postData.static
  await ajax.post('/party', postData)
  uni.navigateBack()
}

async function del() {
  const res = await uni.showModal({ title: '确认删除' })
  if (res.confirm) {
    await ajax.post<any>(`/party/${model.id}/del`)
    uni.switchTab({
      url: '/pages/party/partys?refersh=true',
      success() {
        let pages = getCurrentPages();
        pages[pages.length - 1].onLoad()
      }
    })
  }
}
</script>

<style lang="scss">


 .map{
	 box-shadow: 0 0 15upx rgba(0,0,0,.2);
	 padding: 10upx;
	     box-sizing: border-box;
		 background-color: #fff;
 }

 .line-1{
	 
 }
 .create-panle{
	 padding: 20rpx;color: rgba(255, 255, 255, 0.7);
	 ::v-deep {
		 input{
			 color:#fff;
		 }
		 .icon-arrow{
			 color:#dbdbdb59 !important;
		 }
		 .my-cell,my-cell {
		 		 &.right{
					color:#fff; 
				 }
		}
	  
	 }
 }
::v-deep {
	.uni-datetime-picker-btn{
		color: #4d4d88 !important;
	}
	.uni-datetime-picker-popup{
		color: #333 !important;
	}
	.uni-calendar__content {
		background-color: $uni-bg-color-grey !important;
	}
	.uni-calendar__header-text {
		color: $uni-text-color !important;
	}
	.uni-calendar__header-btn{
		border-color: $uni-text-color !important;
	}
	.uni-calendar-item__weeks-box-text{
		color:$uni-color-success  !important;

	}
	.uni-calendar-item--disable{
		.uni-calendar-item__weeks-box-text{
			color:$uni-text-color-grey !important;
		}
	}
	.uni-calendar-item__weeks-box-item{
		&.uni-calendar-item--checked {
			border-radius: 8px !important;
			border-width: 2px !important;
			border-color: $uni-color-primary !important;
			background-color: $uni-color-success !important;
			.uni-calendar-item__weeks-box-text{
				color: $uni-color-primary !important;
			}
		}
	}
	.uni-calendar__weeks-day{
		border-bottom-color: #F5F5F533 !important;
	}
	.uni-date-changed,
	.uni-calendar--fixed-top{
		border-top-color: #F5F5F533 !important;
	}
	.uni-datetime-picker-timebox-pointer,
	.uni-date-changed--time-date{
		color: #fff !important;
	}
	.uni-datetime-picker-disabled{
		opacity: .7 !important;
	}
	.uni-datetime-picker--btn{
		background-color: $uni-color-success !important;
		color: $uni-color-primary !important;
		border-radius: 8px !important;
		
	}
}

</style>
