<template>
	<view class="page-imgbg container" style="height: 100vh;" :style="{'background-image':$asset('yellow1','bg')}">
		<myListAsync :pageSize="15" style="height: 100%;" url="/my/posts" ref="ml">
			<view class="line-1" style="margin-bottom: 20rpx;background: rgba(255,255,255,.1);padding: 10rpx;" slot="item" slot-scope="{ item, _index, scope }">
				<postItem allowEdit @del="item => del(item, _index)" :d="item"></postItem>
			</view>
		</myListAsync>
	</view>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import ajax from '../../common/ajax'
import postItem from "@/components/logic/post-item.vue"

const ml = ref()

async function del(item: any, idx: number) {
	try {
		await ajax.post(`/my/post/${item.id}/del`)
		ml.value.remove(idx)
	} catch (e) {
		console.error('删除动态失败', e)
	}
}
</script>

<style lang="scss">
	page{
		background-color:$uni-color-warning;
	}
</style>
