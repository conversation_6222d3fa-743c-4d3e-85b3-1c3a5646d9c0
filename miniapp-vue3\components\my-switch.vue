<!--
标签选择

<my-switch size="sm"  :d="enums.venuePaid" v-model="model.paid"></my-switch>

model:
	v-model

大小(size):
	sm || mini

props:
	label: //显示的字段
	key: //值的字段
数据格式(d): 
	['aaa','bbb','ccc']
	{1:'aaa',2:'bbb':'ccc'}
	[{key:'1',label:'aaa'},{key:'2',label:'bbb'},{key:'3',label:'ccc'}] 需指定props:{key,label}对应的字段
-->
<template>
	<view class="my-switch" :class="size">
		<view @tap="changed(val,idx)" :key="idx" :class="{selected:value2==getValue(val)}" v-for="val,idx in d">{{getLabel(val)}}</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				value2: ''
			};
		},
		watch: {
			'modelValue': {
				immediate: true,
				handler(v) {
					this.value2 = v;
				}
			}
		},
		props: {
			modelValue: {
				default: function() {
					return ''
				}
			},
			size: {
				type: String,
				default () {
					return ''
				}
			},
			props: {
				type: Object,
				default () {
					return {
						key: '',
						label: '',
					}
				}
			},
			//支付数组和JSON
			d: {
				type: Object,
			}
		},
		methods: {
			getValue(item, idx) {
				if (item === null) {
					return null
				};
				if (this.d instanceof Array) {
					if (this.props.key) {
						return item[this.props.key]
					} else {
						return item;
					}
				} else {//是enum
					let x =  Object.entries(this.d).find(v=>v[1]==item)
					if(x){
						return x[0]
					}else{
						return null
					}
					
				}
			},
			getLabel(item) {
				if (this.d instanceof Array) {
					if (this.props.label) {
						return item[this.props.label]
					} else {
						return item;
					}
				} else {
					return item;
				}
			},
			changed(item, idx) {
				if (this.value2 == this.getValue(item, idx)) {
					item = null;
					idx = null
				}
				if (!(this.d instanceof Array)) {
					this.value2 = idx;
				}else{
					this.value2 = item;
				}
				
				let v = this.getValue(item,idx);
				this.$emit('update:modelValue', v);
				this.$emit('changed', v);
			}
		}
	}
</script>

<style lang="scss">
	.my-switch {
		&>view {
			display: inline-block;
			line-height: 2;
			background: $uni-bg-color-grey;
			border: 1upx solid $uni-bg-color;
			text-align: center;
			padding: 0 20upx;

			&:first-of-type {
				border-radius: 4px 0 0 4px;
				border-right: 0;
			}

			&:last-of-type {
				border-radius: 0 4px 4px 0;
				border-left: 0;
			}

			&.selected {

				border-color: $uni-text-color-inverse;
				background: $uni-color-success;
				color: $uni-text-color-inverse;
			}
			
		}
		&.warn {
			&>view{
				&.selected {
					border-color: $uni-color-warning;
					background: $uni-color-warning;
					color: $uni-text-color-inverse;
					
				}
			}
		}
		&.sm {
			&>view {
				font-size: .85em;
			}
		}

		&.mini {
			&>view {
				font-size: .7em;
			}
		}
	}
</style>
