<!--
标签选择

<my-switch size="sm"  :source="enums.venuePaid" v-model="model.paid"></my-switch>

model:
	v-model

大小(size):
	sm || mini

props:
	label: //显示的字段
	key: //值的字段
数据格式(d): 
	['aaa','bbb','ccc']
	{1:'aaa',2:'bbb':'ccc'}
	[{key:'1',label:'aaa'},{key:'2',label:'bbb'},{key:'3',label:'ccc'}] 需指定props:{key,label}对应的字段
-->
<template>
	<view class="my-switch" :class="size">
		<view class="my-switch-item"
		v-for="val,key,idx in props.source"
		@tap="changed(val,key)" 
		:key="idx" 
		:class="{selected:value2==getValue(val)}" >
		{{getLabel(val)}}
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, watch, defineProps, defineEmits, withDefaults } from 'vue'

// 定义接口类型
interface PropsConfig {
	key: string
	label: string
}

interface Props {
	modelValue?: string | number | null
	size?: ''| 'sm' | 'mini'
	props?: PropsConfig
	source?: Array<any> | Record<string, any>
}

// 定义props with 类型和默认值
const props = withDefaults(defineProps<Props>(), {
	modelValue: '',
	size: '',
	props: () => ({
		key: '',
		label: ''
	}),
	source: ()=>[]
})

// 定义 emits
const emit = defineEmits(['update:modelValue', 'changed'])

// 响应式数据
const value2 = ref<string | number | null>('')

// 监听 modelValue 变化
watch(() => props.modelValue, (v: string | number | null) => {
	value2.value = v
}, { immediate: true })

// 方法
const getValue = (item: any, idx?: number | null): string | number | null => {
	if (item === null) {
		return null
	}
	if (props.source instanceof Array) {
		if (props.props?.key) {
			return item[props.props.key]
		} else {
			return item
		}
	} else {//是enum
		let x = Object.entries(props.source || {}).find(v => v[1] == item)
		if (x) {
			return x[0]
		} else {
			return null
		}
	}
}

const getLabel = (item: any): string => {
	if (props.source instanceof Array) {
		if (props.props?.label) {
			return item[props.props.label]
		} else {
			return item
		}
	} else {
		return item
	}
}

const changed = (item: any, idx: any): void => {
	let currentItem: any = item
	let currentIdx: number | null = idx

	if (value2.value == getValue(item, idx)) {
		currentItem = null
		currentIdx = null
	}

	if (!(props.source instanceof Array)) {
		value2.value = currentIdx
	} else {
		value2.value = currentItem
	}

	let v = getValue(currentItem, currentIdx)
	emit('update:modelValue', v)
	emit('changed', v)
}
</script>

<style lang="scss" scoped>
	.my-switch {
		.my-switch-item {
			display: inline-block;
			line-height: 2;
			background: $uni-bg-color-grey;
			border: 1upx solid $uni-border-color;
			text-align: center;
			padding: 0 20upx;
			&:first-of-type {
				border-radius: 4px 0 0 4px;
				border-right: 0;
			}

			&:last-of-type {
				border-radius: 0 4px 4px 0;
				border-left: 0;
			}

			&.selected {

				border-color: $uni-text-color-inverse;
				background: $uni-color-success;
				color: $uni-color-primary
			}
			
		}
		&.warn {
			&>view{
				&.selected {
					border-color: $uni-color-warning;
					background: $uni-color-warning;
					color: $uni-text-color-inverse;
					
				}
			}
		}
		&.sm {
			&>view {
				font-size: .85em;
			}
		}

		&.mini {
			&>view {
				font-size: .7em;
			}
		}
	}
</style>
