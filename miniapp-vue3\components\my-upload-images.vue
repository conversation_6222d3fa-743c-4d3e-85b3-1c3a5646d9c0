<!-- 多图片上传，支持qn,local,oss 2025-7-28 -->
<template>
	<view class="my-imgUpload" :class="{
		'has-cover':hasCover,
		}">
		<block v-for="(item, idx) in imageList" :key="idx">

			<view class="upload-item" :class="{error:!(item.state=='uploadWait' || item.state=='uploading' || item.state=='ok')}"
				@click="previewImage(idx)">
				<view class="upload-img">
					<slot name="default" :src="item.filePath">
						<view class="upload-item-content-default" :class="size"
							:style="{'backgroundImage':`url(${item.filePath})`}" />
					</slot>
				</view>
				<view class="upload-del-btn" @click.stop="delImage(idx)" v-if="!disabelDel" />
				<view class="upload-progress" v-if="item.progress < 100">{{item.progress}}%</view>
			</view>
		</block>

		<view class="upload-item upload-item-addbtn" :date-limitTxt="imageList.length+'/'+max" v-if="rduLength > 0"
			@click="choose">
			<slot name="default">
				<view class="upload-item-content-default upload-item-content-default-addbtn" :class="size" />
			</slot>

		</view>
	</view>
</template>

<script lang="ts" setup>
	import { imgUtil } from '../common/funs';
import ajax, { API_HOST } from '@/common/ajax'
	import { computed, getCurrentInstance, nextTick, ref, toRefs, watch } from "vue";
	type IUploadImgState = 'ok' | 'uploadWait' | 'uploading' | 'uploadError' | 'sizeError' | 'typeError'
	type IUploadImg = {
		filePath : string,
		progress : number,
		state : IUploadImgState,
		size : number,
		key : string,
		label : string,
	}
	type IFile = {
		path : string,
		lastModified : number,
		lastModifiedDate : Date,
		name : string,
		size : number,
		type : string,
		webkitRelativePath : string
	}
	type IChooseImageRes = {
		errMsg : string,
		tempFilePaths : string[],
		tempFiles : IFile[]
	}
	type IUploadProps = {
		modelValue : string | string[],
		type: 'social' | 'userAvatar' //影响上传到server的子路径
		hasCover ?: boolean,
		max ?: number,//可上传总数量
		isParalleUpload ?: boolean,//同步上传
		isAutoUpload ?: boolean,//选择图片后自动上传
		size ?: string,
		disabelDel?: boolean,
		maxWidth?:number,
		maxHeight?:number,
		compress?:boolean,
		compressSize?:{
			w:number,h:number
		}
		random?:boolean,
		provider?:"qn"|"al"|"oss",
		uploadPath?:string,
		
	}
const that = getCurrentInstance()
	//{ modelValue, hasCover, max, isParalleUpload, isAutoUpload, size, disabelDel, type }  解构失去响应式，使用toRefs后出错，但不报错
	const props = withDefaults(defineProps<IUploadProps>(),
		{
			modelValue: () => [],
			hasCover: false,
			max: 9,
			isParalleUpload: false,
			isAutoUpload: true,
			size: "social",
			maxWidth:undefined,
			maxHeight:undefined,
			compress:true,
			compressSize:{
				w:1920,
				h:1080
			},
			random:undefined,
			provider: "qn",
			uploadPath:"",
		});

	
	const emit = defineEmits(['update:modelValue','changeReady'])

	const imageList = ref<IUploadImg[]>([])
	const doing = ref(false);
	const _updateModelValueByUploaded = ref(false);
	
	
	function showError(e : any) {
		uni.showToast({
			title: "上传出错: " + (e.message ?? e),
			icon: 'none',
			mask: true,
			duration: 2000
		});
	}
	//可添加的图片个数
	const rduLength = computed(() => {
		return props.max - imageList.value.length;
	})

	watch(() => props.modelValue, (v) => {
		
		let vals = [];
		if (v instanceof Array) {
			vals = [...v];
		} else if (typeof (v) == 'string') {
			vals = [v];
		} else {
			return;
		}
		if (_updateModelValueByUploaded.value) {
			_updateModelValueByUploaded.value = false;
			return
		} else {
			imageList.value = vals.slice(0, props.max).map((imgPath : string, idx : number) => {
				return {
					filePath: imgPath,
					progress: 100,
					key: new Date().getTime() + "_" + idx,
					size: 0,
					state: "ok",
					label: '',
				}

			})
		}
		/*
			//更新v-model时，只增加未存在的
			vals.forEach((imgPath:string,idx:number)=>{
				if(!imageList.value.find(d=>d.filePath == imgPath)){
					//todo blob
					imageList.value.push({
						filePath:imgPath,
						progress:100,
						key:new Date().getTime()+"_"+idx,
						size:0,
						state:"ok",
						label:'',
					})
			}
			})
		*/
	
	},{
		immediate:true
	})
	//外部添加现成的图片 
	function add(url:string){
		if(props.max==1&&(typeof(props.modelValue=='string'))){
			imageList.value = [{
				filePath:url,
				progress:100,
				key:'',
				size:0,
				state:"ok",
				label:'',
			}];
			changed();
		}else{
			if(imageList.value.length<props.max){
				imageList.value.push({
					filePath:url,
					progress:100,
					key:new Date().getTime()+"_"+imageList.value.length,
					size:0,
					state:"ok",
					label:'',
				})
			};
			changed();
		}

	}
	//选择图片
	function choose() {
		
		uni.chooseImage({
			count: (props.max==1&&(typeof(props.modelValue=='string')))?1:rduLength.value, //最多可以选择的图片张数，默认9
			sizeType: ['original', 'compressed'], //original 原图，compressed 压缩图，默认二者都有
			sourceType: ['album'], //album 从相册选图，camera 使用相机，默认二者都有
			success: (res : IChooseImageRes) => {
				const limitSize = 5 * 1024 * 1000;
				const uImgs : IUploadImg[] = res.tempFiles.map((v, idx) : IUploadImg => {
					let state:IUploadImgState = "uploadWait";
					
					
					if (v.size > limitSize) {
						state = "sizeError";
						showError(`图片(${idx + 1})文件大小超出5M`);
					}
					//有些图片(jpg)没有v.type，奇怪
					// if(!['image/png','image/jpeg','image/png','image/gif','image/bmp'].includes(v.type)){
					// 	state = "typeError";
					// 	showError(`图片格式不正确:`+v.type);
					// }
					

					return {
						filePath: v.path,
						progress: 0,
						state,
						size: v.size,
						key: '',
						label: ''
					}
				})
				uploadReady(uImgs);

			}
		});
	}
	
	 function uploadReady(uImgs : IUploadImg[]){
		if(props.max==1&&(typeof(props.modelValue=='string'))){
			imageList.value =[uImgs[0]]
		}else{
			uImgs.forEach(async (ui) => {
				//压缩
				if (props.compress) {
					console.log('原图片',ui.filePath)
					ui.filePath = await imgUtil.compressImage(that,'photo_canvas',ui.filePath,
					props.compressSize,
					{ //设置canvas尺寸，宽度比例压缩
						h:500,
						w:750
					},
					);
					console.log('压缩后图片',ui.filePath)
				}
				imageList.value.push(ui);
			})
			
			if (props.isAutoUpload) {
				upload(imageList.value);
			}
		}
		emit('changeReady',imageList.value)
	}
	//上传图片 
	async function upload(uImgs? : IUploadImg[]) {
	  
	  let uploadFn:(img:IUploadImg,path:string)=>Promise<string>;
	  if (props.provider == 'oss') {
		uploadFn = uploadOss
	  } else if (props.provider == 'qn') {
		uploadFn = uploadQn
	  } else {
		uploadFn = uploadLocal
	  }
		return new Promise(async (r, j) => {
			doing.value = true;
			uImgs ??= imageList.value;//如果uimgs为空，使用imageList，for 手动上传
			let _imageWaitList : IUploadImg[] = uImgs.filter(v=>v.state == "uploadWait")

			if (!props.isParalleUpload) { //串行上传

				for (let i = 0; i < _imageWaitList.length; i++) {
					
					const uImg:IUploadImg = _imageWaitList[i];
					await uploadFn(uImg,props.uploadPath).catch(e => {
						showError(e);
						return "";
					})
					changed()
					doing.value = false;
				}
				r(true);

			} else { //并行上传

				const tasks : Promise<string>[] = [];
				_imageWaitList.forEach(uImg => {
					
					tasks.push(uploadFn(uImg,props.uploadPath).then((imgUrl) => {
						uImg.filePath = imgUrl
						changed()
						return "";
					}).catch(e => {
						showError(e)
						return "";
					})
					)
				})
				await Promise.all(tasks).finally(() => {
					doing.value = false;
				})
				r(true);
			}

		})

	}

//删除图片
	function delImage(index : number) {
		uni.showModal({
			content: '确定要放弃这张图片么？',
			success: (confirmRes) => {
				if (confirmRes.confirm) {
					imageList.value.splice(index, 1);
					changed()
				}
			}
		});
	}
	//预览图片
	function previewImage(index : number) {
		const urls:string[] = [];
		imageList.value.forEach((item : IUploadImg) => {
			urls.push(item.filePath);
		})
		uni.previewImage({
			current: urls[index],
			urls: urls,
			indicator: "number"
		})
	}
	function changed() {
		_updateModelValueByUploaded.value = true;
		var newModelValue = getData();
		nextTick(() => {
			emit('update:modelValue',newModelValue);
		})


	}
	function getData() {
		if(typeof(props.modelValue) =='string' && props.max ==1){
			return imageList.value[0].filePath
		}else{
			
			let data = []
			for (let i in imageList.value) {
				const uImg = imageList.value[i];
				if (uImg.progress === 100) {
					data.push(
						uImg.filePath
					)
				}
			}
			return data;
		}

	}
	
	async function uploadLocal(uImg : IUploadImg,) : Promise<string> {
		return new Promise((resolve, reject) => {
			//发送给后端的附加参数
			const formData:any = {
				type: props.type,
				maxWidth:props.maxWidth,
				maxHeight:props.maxHeight,
				compress:props.compress,
				random:props.random
			};
			for(var k  in formData){
				if(formData[k]==undefined){
					delete formData[k];
				}
			}
			uImg.progress = 0;
			uImg.state = "uploading";
			let uploadTask = uni.uploadFile({
				url: API_HOST + '/common/upload?type=' + props.type,
				filePath: uImg.filePath,
				name: 'files',
				formData,
				header: ajax.getRequestHeader(),
				success(res) {
	
					if (res.statusCode == 200) {
						const uploadFileRes = JSON.parse(res.data) || {};
						if (uploadFileRes.code === 0 && uploadFileRes.data?.url) {
							uImg.filePath = uploadFileRes.data.url;
							uImg.state = "ok";
							resolve(uploadFileRes.data.url);
	
						} else {
							uImg.state = "uploadError";
							reject(uploadFileRes.message);
						}
	
					} else {
						uImg.state = "uploading";
	
						reject(res.statusCode)
					}
	
				},
				fail(e) {
					uImg.state = "uploadError"
					reject('upload fail');
				},
				complete() {
					//todo
				}
			});
			//上传进度
			uploadTask.onProgressUpdate((progressRes) => {
				uImg.progress = progressRes.progress;
			});
		});
	}
	
	
	let uploadPolicyInfo:any;
	const uploadQn = async (imgModel:IUploadImg,path:string):Promise<string> => {
		const uploadUrl = 'https://up-z2.qiniup.com';
		const uploadedUrl = 'http://tu.1caiba.com/'; //上传后文件地址
		return new Promise(async (r, j) => {
			if (!uploadPolicyInfo || !uploadPolicyInfo.token) {
				uploadPolicyInfo = {
					key: '',
					token: '',
					url: ''
				};
				uploadPolicyInfo.token = await ajax.get('/common/getQnToken');
			}
			uploadPolicyInfo.key = path + '/' + new Date().getTime() + imgModel.filePath.substr(imgModel.filePath.lastIndexOf('.'))
			let uploadParams = {
				name: 'file',
				url: uploadUrl,
				filePath: imgModel.filePath,
				formData: {
					key: uploadPolicyInfo.key,
					token: uploadPolicyInfo.token,
					success_action_status: '200',
					// 'Signature': uploadPolicyInfo.Signature,
					// 'x-cos-security-token': uploadPolicyInfo.XCosSecurityToken,
					'Content-Type': 'application/json',
				},
			}
	
			const uploadTask = uni.uploadFile({
				...uploadParams,
				success: (res) => {
					if (res.data) {
						r(uploadedUrl + uploadParams.formData.key)
					} else {
						j(res);
					}
				},
				fail: (err) => {
					j(err.errMsg)
				}
			})
			//上传进度
			uploadTask.onProgressUpdate((progressRes) => {
				imgModel.progress = progressRes.progress;
			});
		})
	}
	
	//阿里
	const uploadOss = async (imgModel:IUploadImg,path:string):Promise<string> => {

		return new Promise(async (r, j) => {

			if (!uploadPolicyInfo || (Date.now() / 1000 + 30) > uploadPolicyInfo.expiredTime) {
				const r2 = await ajax.get<any>('/upload/cosParams');
				//todo AuthData
				const AuthData = {
					SecretId: r2.credential.credentials.tmpSecretId,
					SecretKey: r2.credential.credentials.tmpSecretKey,
					Method: 'POST',
					Pathname: "/" //+r2.path
				};
				uploadPolicyInfo = {
					path: "/" + r2.path,
					url: 'https://' + r2.bucket + '.cos.' + r2.region + '.myqcloud.com/',
					Signature: AuthData,
					'XCosSecurityToken': r2.credential.credentials.sessionToken,
					expiredTime: r2.credential.expiredTime
				};
			}

			imgModel.key = uploadPolicyInfo.path + imgModel.key + imgModel.filePath.substr(imgModel
				.filePath.lastIndexOf(
					'.'))

			let uploadParams = {
				name: 'file',
				url: uploadPolicyInfo.url,
				filePath: imgModel.filePath,
				formData: {
					key: imgModel.key,
					success_action_status: '200',
					'Signature': uploadPolicyInfo.Signature,
					'x-cos-security-token': uploadPolicyInfo.XCosSecurityToken,
					'Content-Type': '',
				},
			}

			// console.log('uploadOss =>')
			// console.log(uploadParams)
			const uploadTask = uni.uploadFile({
				...uploadParams,
				success: (res:any) => {
					console.log('success uploadOss=>' + res.header.Location)
					if (res.statusCode == 200) {
						r(res.header.Location)
					} else {
						j(res);
					}
				},
				fail: (err) => {
					j(err.errMsg)
				}
			})

			//上传进度
			uploadTask.onProgressUpdate((progressRes) => {
				imgModel.progress = progressRes.progress;
			});
		})

	}


	defineExpose({
		add,
		upload,
		choose,
	})
</script>

<style lang="scss" scoped>
	.my-imgUpload {
		// padding:24upx 0 0 28upx;
		display: flex;
		gap: var(--space);
		justify-content:space-around;
		flex-wrap: wrap;
		--space: 30upx;
		--icon-size: 100upx;
		--width: 200upx;
		--height: 200upx;
	}

	.upload-item {
		position: relative;
		display: inline-block;
		.upload-img {
			display: inline;
		}
		&.error {
			&:before {
				position: absolute;
				content: "+";
				color: red;
				font-size: var(--icon-size);
				transform: rotate(45deg);
				font-family: fantasy;
				text-align: center;
				pointer-events: none;
				left: 0;
				right: 0;
				width: 100%;
				height: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
				z-index: 10;
			}

			.upload-img {
				filter: grayscale(1);
				opacity: .5;
			}
			.upload-progress{
				display: none;
			}
		}

		.upload-del-btn {
			position: absolute;
			right: -10upx;
			top: -10upx;
			width: 48upx;
			height: 48upx;
			border: 2upx solid  $uni-color-success;
			// border-right-color: transparent;
			// border-top-color: transparent;
			border-radius: 100px;
			background-color:$uni-color-primary;
			display: flex;
			align-items: center;
			justify-content: center;
			z-index: 99;
			outline: 2upx solid transparent;

			&::after {
				content: "";
				height: 6upx;
				background-color:  $uni-color-success;
				width: 20upx;
			}
		}

		.upload-progress {
			position: absolute;
			left: 0;
			top: 0;
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 100%;
			background-color: rgba(0, 0, 0, .2);
			color: #fff;
			font-size: 24upx;
			border-radius: 8upx;
			pointer-events: none;
		}

		&.has-cover .upload-item:first-child::after {
			position: absolute;
			top: 40%;
			left: 25%;
			line-height: 2;
			content: "封面";
			display: block;
			width: 50%;
			text-align: center;
			font-size: $uni-font-size-sm;
			background: rgba(0, 0, 0, .4);
			border-radius: 16upx;
			color: #fff;
			pointer-events: none;
		}
	}

	.upload-item-content-default {
		border-radius: 8upx;
		position: relative;
		width: var(--width);
		height: var(--height);
		background-size: cover;
		background-position: center center;
		background-repeat: no-repeat;
		border: 2upx solid $uni-border-color;
		background-color:$uni-bg-color-grey;
		box-shadow: inset 0 0 3em rgba(0, 0, 0, .5);;

		&.sm {
			--width: 120upx;
			--height: 140upx;
			--icon-size: 50upx;
			--space: 10upx;
		}

		&.upload-item-content-default-addbtn {
			border: 2upx dashed $uni-color-success;
			&:before {
				content: "添加图片";
				position: absolute;
				text-align: center;
				opacity: .4;
				top: 65%;
				font-size: $uni-font-size-sm;
				left: 0%;
				width: 100%;
			}

			&:after {
				content: "";
				position: absolute;
				opacity: .2;
				right: 0%;
				top: 50%;
				transform: translateX(calc(var(--width) / -3.5)) translateY(calc(var(--width) / -3));
				filter:brightness(1000%);
				width: calc(var(--width) / 2.5);
				height: calc(var(--height) / 2.5);
				background-position: center top;
				background-repeat: no-repeat;
				background-size: cover;
				background-image: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBzdGFuZGFsb25lPSJubyI/PjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+PHN2ZyB0PSIxNjkxMzM1ODcwNzcxIiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjE0OTMiIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCI+PHBhdGggZD0iTTUwOC44IDQxMi45Yy05Mi42IDAtMTY3LjkgNzUuMy0xNjcuOSAxNjcuOXM3NS4zIDE2Ny45IDE2Ny45IDE2Ny45IDE2Ny45LTc1LjMgMTY3LjktMTY3LjktNzUuMy0xNjcuOS0xNjcuOS0xNjcuOXogbTAgMjg1LjljLTY1IDAtMTE3LjktNTIuOS0xMTcuOS0xMTcuOVM0NDMuOCA0NjMgNTA4LjggNDYzczExNy45IDUyLjkgMTE3LjkgMTE3LjktNTIuOSAxMTcuOS0xMTcuOSAxMTcuOXoiIGZpbGw9IiMyYzJjMmMiIHAtaWQ9IjE0OTQiPjwvcGF0aD48cGF0aCBkPSJNODMwLjggMjY4LjZINjY2LjRsLTguNC00MmMtNS41LTI3LjctMzAuMS00Ny45LTU4LjQtNDcuOUg0MjQuNGMtMjguMyAwLTUyLjggMjAuMS01OC40IDQ3LjlsLTguNCA0MkgxOTMuMmMtNDMuNyAwLTc5LjIgMzUuNS03OS4yIDc5LjJ2NDE4LjNjMCA0My43IDM1LjUgNzkuMiA3OS4yIDc5LjJoNjM3LjZjNDMuNyAwIDc5LjItMzUuNSA3OS4yLTc5LjJWMzQ3LjhjMC00My43LTM1LjUtNzkuMi03OS4yLTc5LjJ6TTg2MCA3NjYuMWMwIDE2LjEtMTMuMSAyOS4yLTI5LjIgMjkuMkgxOTMuMmMtMTYuMSAwLTI5LjItMTMuMS0yOS4yLTI5LjJWMzQ3LjhjMC0xNi4xIDEzLjEtMjkuMiAyOS4yLTI5LjJoMTg0LjljMTEuOSAwIDIyLjItOC40IDI0LjUtMjAuMWwxMi40LTYyLjFjMC45LTQuNCA0LjgtNy43IDkuNC03LjdoMTc1LjJjNC41IDAgOC41IDMuMiA5LjQgNy43bDEyLjQgNjIuMWMyLjMgMTEuNyAxMi42IDIwLjEgMjQuNSAyMC4xaDE4NC45YzE2LjEgMCAyOS4yIDEzLjEgMjkuMiAyOS4ydjQxOC4zeiIgZmlsbD0iIzJjMmMyYyIgcC1pZD0iMTQ5NSI+PC9wYXRoPjxwYXRoIGQ9Ik03ODMuNCAzODQuMmgtNzkuMWMtMTMuOCAwLTI1IDExLjItMjUgMjVzMTEuMiAyNSAyNSAyNWg3OS4xYzEzLjggMCAyNS0xMS4yIDI1LTI1cy0xMS4yLTI1LTI1LTI1eiIgZmlsbD0iIzJjMmMyYyIgcC1pZD0iMTQ5NiI+PC9wYXRoPjwvc3ZnPg==");
			}

			&:active {
				background-color: $uni-bg-color-mask;
			}

			.upload-item-addbtn-txt {
				position: absolute;
				top: calc(var(--height) / 10);
				left: calc(var(--height) / 10);
				text-align: center;
				color: $uni-text-color;
				font-size: 18upx;
			}
		}


	}

	.upload-item-addbtn {}
</style>