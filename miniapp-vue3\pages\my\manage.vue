<template>
	<view class="container f-sm f-color-light">
		<view v-for="item,idx in d.list" :key="idx">
			<myCell :label="item.name" line="1" v-if="item.type == 0">
				<mySwitch slot="rightSlot" :d="item.desc" v-model="item.value" @changed="(v) => save(item.key, v)"></mySwitch>
			</myCell>
			<view class="my-card" v-if="item.type == 1">
				<view class="">
					{{ item.name }}
				</view>
				<textarea class="my-box" style="width: 100%;" v-model="item.value"></textarea>
				<button class="my-button success sm" @click="save(item.key, item.value)">保存</button>
			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
import { reactive, onMounted } from 'vue'
import { appStore } from '../../store'
import ajax from '../../common/ajax'

const app = appStore()

const d = reactive({
	list: []
})

onMounted(() => {
	load()
})

async function load() {
	try {
		const res = await ajax.get<any[]>('/sys/items')
		res.sort((a, b) => { return a.index < b.index ? 1 : -1 })
		d.list = res.map((v: any) => {
			if (v.desc) {
				try {
					v.desc = JSON.parse(v.desc)
				} catch (e) {
					// 解析失败时保持原值
				}
			}
			return v
		})
	} catch (e) {
		console.error('加载系统设置失败', e)
	}
}

async function save(key: string, value: any) {
	try {
		await ajax.post('/sys/item', { key, value })
		app.updateSys({ key, value })
	} catch (e) {
		console.error('保存设置失败', e)
	}
}
</script>

<style lang="scss">
page{
	
}
</style>
