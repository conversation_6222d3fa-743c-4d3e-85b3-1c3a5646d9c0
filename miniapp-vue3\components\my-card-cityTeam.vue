<template>
	<view class="cityTeam-card " style="gap: 10px;flex-flow: row-reverse;">
		<view>
			<view class="f-color-gray">
				cityTeam
			</view>
			<view class="f-lg" style="margin: 5px 0;">
				团队
			</view>
			<navigator class="btn cy-right-arrow" url="/pages/cityTeam/list" />
		</view>
		<view class="cityTeam-list">
			<view class="item" v-for="item,idx in list" :key="idx" @tap="goToDetail(item)">
				<view class="">
					<view class="flex flex-center">
						<text class="my-title f-lg f-b">{{item.name}}</text>
						
					</view>
					<view class="mpl10 f-color-gray" v-if="item.leader">队长:{{item.leader}}</view>
					<view class="mpl10 f-color-gray f-sm">
						{{item.intro}}
					</view>
				</view>
				<view class="" style="width: 110px	;">
					<view class="f-color-success" style="text-align: right;line-height: 2;">
						{{item.size?(item.size+'人'):'未知'}}
					</view>
					<view class="flex" style="flex-wrap: wrap;">
						<view class="my-tag sm" style="width:50px" v-for="t,tIdx in parseBinaryEnums(app.enums['cityTeamType'],item.type)">
							{{t.name}}
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { userStore } from '../store/user';
import ajax from '../common/ajax';
import { parseBinaryEnums } from "../common/funs"
import { appStore } from '../store';

const list = ref<ICityTeam[]>([])
const isLoaded = ref(false)
const us = userStore();
const app = appStore();
watch(()=>us.tryLocal.divisionCode,(n,o)=>{
	if(n&& n!=o){
		load();
	}
		
},{immediate:true})

async function load(){
	const res = await ajax.get<ICityTeam[]>('/cityTeam/top',{
		divisionCode:us.tryLocal.divisionCode,
		top:3,
		isRandom:true
	});
	

	list.value = res.map(v=>{
		let imageArr = (v.images||"").split(",");
		var x=  {
			...v,
			cover:imageArr[0],
			imageArr
		};
		return x;
	});


}

// 跳转到团队详情页
function goToDetail(item: ICityTeam) {
	uni.navigateTo({
		url: `/pages/cityTeam/detail?id=${item.id}`
	});
}
</script>

<style lang="scss" scoped>

.cityTeam-card{
	border-radius: 12px;
	display: flex;
	justify-content: space-between;
	//background: linear-gradient(-90deg, rgba(42, 47, 55, 1),rgba(42, 47, 55, 0) 100%);
}
.cityTeam-list{
	flex: 100%;
	display: flex;
	flex-direction: column;
	gap: 8px;
	.item{
		padding:10px;
		position: relative;
		//background-color: $uni-bg-color-hover;
		background-color: $uni-bg-color-grey;
		border-radius: 5px;
		display: flex;
		justify-content: space-between;
		.title{
			text-shadow: 0 0 2px black;
			letter-spacing: 1px;
			position: relative;
			z-index: 1;
			font-weight: 400;
			
			width: 200px;
			  display: -webkit-box; /* 设置为WebKit内核的弹性盒子模型 */
			  -webkit-box-orient: vertical; /* 垂直排列 */
			  -webkit-line-clamp: 2; /* 限制显示两行 */
			  overflow: hidden; /* 隐藏超出范围的内容 */
			  text-overflow: ellipsis; /* 使用省略号 */
		}
	}
	
}
</style>