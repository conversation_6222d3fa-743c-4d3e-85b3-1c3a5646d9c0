declare type IUser={
	id:string,
	avatar:string
	nickName:string
}
declare type IMyUser = IUser & {
	name : string
	gender : string
	province : string
	city : string
	county : string
	level : number
	phone : string
	sign : string
	divisionCode : number,
	isMember : boolean
}

declare type IAccount = {
	type : number,
	name : string
}

declare type ILocation = {
	lon ?: number,
	lat ?: number,
	country ?: string,
	province : string,
	city : string,
	area ?: string,
	divisionCode : number
}

declare type ICityCardData = {
	province : string
	city : string
	cityCode : number
	cTeamCount : number
	venueCount : number
	memberCount : number
}

declare type IMyError = "" | "NET" | "TOKEN" | "SERVER"

declare type Ipagen<T> = {
	list : T[]
	pageIndex : number
	pageSize : number
	total : number
}

declare type IParty = {
	id ?: string
	name : string;
	content : string;
	beginDate : string;
	beginDate_s : string;
	divisionCode : number;
	address ?: string;
	lon ?: string;
	lat ?: string;
	type ?: number;
	size ?: number;
	time ?: number;
	parking ?: number;
	paid ?: number;
	venueType ?: number;
	contacter ?: string;
	contacterPhone ?: string;
	venueId ?: string;
	userId ?: string;
	user : {
		id : string,
		nickName : string,
		avatar : string
	}
	isSelf ?: boolean;
	distance?:string

}

declare type IVenue = {
	"id" : string,
	"createdAt" : string,
	"updatedAt" : string,
	"rank" : number,
	"disabled" : number,
	"name" : string,
	"address" : string,
	"lat" : string,
	"lon" : string,
	"city" : string,
	"province" : string,
	"district" : string | null,
	"divisionCode" : 210300,
	"type" : number,
	"able" : number,
	"size" : number,
	"paid" : number,
	"parking" : number,
	"paidIntro" : string,
	"intro" : string,
	"contacter" : string,
	"contacterPhone" : string,
	"tag" : string,
	"images" : string,
	"imagesArr":string[]
	"star" : number,
	"state" : number,
	"createUserId" : string,
	"updateUserId" : string,
	"sportId" : null,
	"static" : {
		"visitCount" : number,
		"hotCount" : number,
		"likeCount" : number,
		"shareCount" : number,
	},
	"user" : IUser

}

declare type ICityTeam = {
	id : string
	name : string
	intro : string
	avatar : string
	content : string
	bak : string
	type : number
	size:number
	country : string
	province : string
	city : string
	district : string
	divisionCode : number
	lat:number
	lon:number;
	address : string
	leader : string
	leaderLink : string
	images : string;
	sportId : string;
	createdUserId : string;
	createdUser : IUser;
	updatedAt:string

}

declare type IDivisonCityMap={
	name:string,
	code:number
	citys:{name:string,code:string}[]
}[]


// declare	const VueInstance:{
// 		tryEnum:(type:string)=>{}
// 	}