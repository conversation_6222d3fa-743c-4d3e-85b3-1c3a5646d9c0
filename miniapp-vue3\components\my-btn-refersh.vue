<template>
	<movable-area style="width: 100vw;height: 100vh;position: absolute;top:0;left:0; background: transparent;">
		<movable-view class="refresh-btn" :x="x" :y="y" direction="all" @change="onChange" @click="$emit('ck')">
			<view class="iconfont icontongbu"></view>
		</movable-view>
	</movable-area>

</template>

<script>
	export default {
		data() {
			return {
				x:300,
				y:600,
			};
		}
	}
</script>

<style lang="scss" scoped>
.refresh-btn{
	
	border-radius: 50%;
	width: 70rpx;
	height: 70rpx;
	background: #fff;
	color: #56da09;
	line-height:66rpx;
	text-align: center;
	opacity: .5;
	    position: fixed;
	    z-index: 99;
	&:active{
		opacity:1
	}
}
</style>
