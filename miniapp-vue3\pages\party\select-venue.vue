<template>
	<view class="body col-flex-justify" style="height: 100vh;" >
		<view class="center">
			<selectCity label="选择城市: " :value="[searchParams.where.province,searchParams.where.city]" @changed="locChanged"></selectCity>
	
		</view>
		<view class="container" style="height: calc(100vh - 80upx);overflow-y:auto; background: linear-gradient(180deg, rgba(14,17,54,1) 0%, rgba(13,68,63,1) 100%, rgba(160,205,34,1) 100%);">
			<view class="flex flex-between venue g-hover" v-for="venue,idx in list" :key="idx" @click="selectClick(venue)">
				<view class="">
					<view class="f-big" style="font-weight: 400;">
						{{venue.name}}
					</view>
					<view class="f-sm " style="opacity: .5;">
						{{venue.address}}
					</view>
				</view>
				<view class="">
					<view class="iconfont iconarrowRight f-color-main "></view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useStore } from 'vuex'
import { onLoad } from '@dcloudio/uni-app'

const store = useStore()
const user = computed(() => store.getters.user)
const base = computed(() => store.getters.base)

const searchParams = reactive({
  where: {
    city: '',
    province: ''
  }
})
const list = ref<any[]>([])

onLoad(async (query: any) => {
  if (query.where) {
    const _w = JSON.parse(decodeURIComponent(query.where))
    searchParams.where = _w
  } else {
    searchParams.where.province = user.value.province || '辽宁'
    searchParams.where.city = user.value.city || '鞍山'
  }
  await load()
})

async function load() {
  list.value = await store.state.$ajax.get('/venuesForSelect', searchParams)
}

function locChanged(v: [string, string]) {
  searchParams.where.province = v[0]
  searchParams.where.city = v[1]
  load()
}

function selectClick(venue: any) {
  uni.$emit('selectVenue', venue)
  uni.navigateBack()
}

function goView(item: any) {
  uni.navigateTo({ url: 'View?id=' + item.id })
}
</script>

<style lang="scss">
	.venue{
		padding: 10upx;
		margin: 10upx;
		border-bottom: 2upx solid rgba(255,255,255,.1);
		&:last-child{
			border-bottom:0;
		}
	}
</style>
