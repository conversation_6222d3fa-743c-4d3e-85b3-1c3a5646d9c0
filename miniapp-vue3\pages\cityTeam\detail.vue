<template>
	<view class="page-container">
		<!-- 页面头部 -->
		<my-page-header :title="d.name" :showBack="true"></my-page-header>

		<!-- 团队基本信息 -->
		<view class="card" style="padding-top: 20px;">
			<view class="g-center">
				<my-user-avatar size="big" :data="{avatar:d.avatar,name:'xxx'}"></my-user-avatar>
				<view class="mpt10 f-title">
					{{ d.name }}
				</view>
				
				<view class="mpt10 f-sm">{{ d.intro }}</view>
			</view>

			<!-- 联系信息 -->
			<view class="contact-section">
				<my-cell label="团队负责人" :right="d.leader" line="2"></my-cell>
				<my-cell label="联系方式" :right="d.leaderLink" rightCls="f-color-success"></my-cell>
			</view>
		</view>

		<!-- 团队图片展示 -->
		<view class="images-card" v-if="teamImages.length > 0">
			<view class="images-header">
				<text class="images-title">团队风采</text>
			</view>
			<view class="images-grid">
				<image v-for="(img, index) in teamImages" :key="index" class="team-image" :src="img" mode="aspectFill"
					@tap="previewImage(index)"></image>
			</view>
		</view>

		<!-- 团队详情信息 -->
		<view class="detail-info-card">
			<my-cell label="团队类型"  line="2">
				<template #rightSlot>
					<view class="flex" style="flex-wrap: wrap; width: 140px;justify-content: flex-end;">
						<view class="my-tag" :key="tIdx"  v-for="t,tIdx in parseBinaryEnums(app.enums['cityTeamType'],d.type)">
							{{t.name}}
						</view>
					</view>
				</template>
			</my-cell>
			<my-cell label="团队规模" :right="d.size" line="2"></my-cell>
			<my-cell label="所在地区" >
				<template #rightSlot>
					<view class="">
						{{ d.province }}{{ d.city }}{{ d.district }}
					</view>
				</template>
			</my-cell>
		</view>



		<!-- 地址信息卡片 -->
		<view class="location-card">
			<view class="">
				常去地点：
			</view>
			<view class="location-header">
				<text class="location-title">{{ d.address }}</text>
			</view>

			<!-- 地图区域 -->
			<view class="map-container">
				<map class="team-map" :latitude="d.lat" :longitude="d.lon" :markers="[{
							id:1,
							width:20,
							height:30,
							latitude:d.lat,
							longitude:d.lon,
							name:d.name
						}]" :show-location="true">
						<cover-view style='width:100%;height:100%'></cover-view></map>
			</view>
		</view>
		
		<view class="flex-center-all flex-gap10 mpt10">
			
			<my-user-avatar :data="d.createdUser" size="sm" showName nameLine></my-user-avatar>

		</view>

		<view class="f-sm f-color-gray mpb10 g-center" style="padding-bottom: 10px;">
			update:{{(d.updatedAt+"").substring(0,11)}} 
		</view>



	</view>
</template>

<script lang="ts" setup>
	import { ref, computed, onMounted } from 'vue';
	import { onLoad, onShareAppMessage } from '@dcloudio/uni-app';
	import ajax from '../../common/ajax';
import { userStore } from '../../store/user';
import { appStore } from '../../store';
import { parseBinaryEnums } from "@/common/funs"
	const app = appStore();
	

	const d = ref<ICityTeam>({
		id: '',
		name: '',
		intro: '',
		avatar: '',
		content: '',
		bak: '',
		type: 0,
		size: 0,
		country: '',
		province: '',
		city: '',
		district: '',
		divisionCode: 0,
		lat: 0,
		lon: 0,
		address: '',
		leader: '',
		leaderLink: '',
		images: '',
		sportId: '',
		createdUserId: '',
		createdUser: {
			id: '',
			avatar: '',
			nickName: ''
		}
	});

	const teamImages = computed(() => {
		if (!d.value.images) return [];
		return d.value.images.split(',').filter(img => img.trim());
	});

	onLoad((params : any) => {
		if (params.id) {
			loadTeamDetail(params.id);
		}
	});

	// 加载团队详情
	async function loadTeamDetail(id : string) {
		try {
			const result = await ajax.get<ICityTeam>(`/cityTeam/${id}`);
			d.value = result;
		} catch (error) {
			console.error('加载团队详情失败:', error);
			uni.showToast({
				title: '加载失败',
				icon: 'error'
			});
		}
	}


	// 拨打电话
	function makeCall() {
		if (d.value.leaderLink) {
			uni.makePhoneCall({
				phoneNumber: d.value.leaderLink
			});
		}
	}

	// 预览图片
	function previewImage(index : number) {
		uni.previewImage({
			urls: teamImages.value,
			current: index
		});
	}

	// 打开导航
	function openNavigation() {
		if (d.value.address) {
			uni.openLocation({
				latitude: d.value.lat,
				longitude: d.value.lon,
				name: d.value.name,
				address: d.value.address
			});
		}
	}

	// 点赞功能
	function toggleLike() {
		uni.showToast({
			title: '点赞成功',
			icon: 'success'
		});
	}

	// 申请加入团队
	function joinTeam() {
		uni.showModal({
			title: '申请加入',
			content: `确定要申请加入${d.value.name}吗？`,
			success: (res) => {
				if (res.confirm) {
					// 这里调用加入团队的API
					uni.showToast({
						title: '申请已提交',
						icon: 'success'
					});
				}
			}
		});
	}

	// 分享团队
	function shareTeam() {
		uni.showShareMenu({
			withShareTicket: true
		});
	}

	// 分享配置
	onShareAppMessage(() => {
		return {
			title: `${d.value.name} - 毽球团队`,
			path: `/pages/cityTeam/detail?id=${d.value.id}`,
			imageUrl: d.value.avatar
		};
	});
</script>

<style lang="scss" scoped>

	/* 位置信息卡片 */
	.location-card {
		// background-color: $uni-bg-color-grey;
		border-radius: 20rpx;
		// padding: 30rpx;
		margin-bottom: 20rpx;

		.location-header {
			margin-bottom: 20rpx;

			.location-title {
				font-size: 32rpx;
				font-weight: 500;
				color: $uni-text-color;
			}
		}

		.map-container {
			height: 400rpx;
			border-radius: 15rpx;
			overflow: hidden;

			.team-map {
				width: 100%;
				height: 100%;
			}
		}
	}

	/* 详情信息卡片 */
	.detail-info-card {
		background-color: $uni-bg-color-grey;
		border-radius: 20rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;

		.info-row {
			display: flex;
			justify-content: space-between;
			align-items: flex-start;
			padding: 25rpx 0;
			border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);

			&:last-child {
				border-bottom: none;
			}

			.info-label {
				font-size: 28rpx;
				color: $uni-text-color-inverse;
				min-width: 160rpx;
			}

			.info-value {
				font-size: 28rpx;
				color: $uni-text-color;
				flex: 1;
				text-align: right;

				&.content-text {
					text-align: left;
					line-height: 1.5;
					margin-left: 20rpx;
				}
			}
		}
	}

	/* 图片展示卡片 */
	.images-card {
		border-radius: 20rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;

		.images-header {
			margin-bottom: 20rpx;

			.images-title {
				font-size: 32rpx;
				font-weight: 500;
				color: $uni-text-color;
			}
		}

		.images-grid {
			display: grid;
			grid-template-columns: repeat(3, 1fr);
			gap: 15rpx;

			.team-image {
				width: 100%;
				height: 200rpx;
				border-radius: 10rpx;
			}
		}
	}



</style>