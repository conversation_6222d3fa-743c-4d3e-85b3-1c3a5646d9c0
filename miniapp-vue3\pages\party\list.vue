<template>
	<z-paging-swiper>
		<template #top>
			<view>
				<my-page-header>
					<view class="flex flex-center" >
						<view class="">
							约毽
						</view>
						<view class="flex flex-center" 
						style="margin-left: 10px;border: 1px solid #ffffff11;padding: 3px 10px;border-radius: 50px;box-shadow: inset 0 0 12px #000;">
							<checkbox :checked="d.onlyViewSelfCity" class="viewSelfCityCheckbox" @tap="d.onlyViewSelfCity=!d.onlyViewSelfCity">只看{{user.tryLocal.city}}</checkbox>
						</view>
					</view>
				</my-page-header>
<!-- 				<selectCity label="选择城市: " :value="[d.queryParams.province,d.queryParams.city]"
					@changed="cityChanged" :allCity="true" :allProvince="true"></selectCity> -->
				<view class="tabs" style="margin-top:-50upx;">
					<view class="tab" :class="{active:d.navCurrent==idx}" v-for="item,idx in d.navs"
						@tap="d.navCurrent=idx" :key="idx">
						{{item.name}}
					</view>
				</view>

					<my-fab v-if="app.sys.allowPushParty==1 || user.isAdmin" url="/pages/party/partyEdit"></my-fab>
			</view>
		</template>

		<view class="swiper-scroll">
			<swiper :indicator-dots="false" :current="d.navCurrent" :duration="500" @animationfinish="swiperChanged">
				<swiper-item v-for="nav,navIdx in d.navs" :key="navIdx">
					<z-paging :show-default-loading-more-text="false" :fixed="false" ref="refs" v-model="nav.data"
						@query="(i:number,s:number)=>load(i,s,false)" :auto="false">
						<navigator class="item-box" :url="`/pages/party/partyView?id=${item.id}`"
							v-for="(item,index) in nav.data" :key="index">
							<my-item-party :item="item"></my-item-party>
						</navigator>
					</z-paging>
				</swiper-item>
				<!-- 				<swiper-item>
					<z-paging :show-default-loading-more-text="false" :fixed="false" :ref="refs[1]"
						v-model="d.navs[1].data" @query="(i:number,s:number)=>load(i,s,true)" :auto="false">
						<navigator class="history-item" :url="`/pages/calculator/calcResult?id=${item.id}`"
							v-for="(item,index) in d.navs[1].data" :key="index">
							<my-item-party :item="item" expireCls='expireCls'></my-item-party>
						</navigator>
					</z-paging>
				</swiper-item> -->
			</swiper>
		</view>
	</z-paging-swiper>
</template>

<script setup lang="ts">
	import { onMounted, reactive, ref, watch } from "vue";
	import { userStore } from "../../store/user";
	import { appStore } from "../../store";
	import { onLoad } from "@dcloudio/uni-app"
	import { formatDate, getDistance } from "../../common/funs";
	import ajax from "../../common/ajax";

	const app = appStore();
	const user = userStore();
	const enums = appStore().enums;
	const refs = ref<any[]>([])
	const d = reactive({
		onlyViewSelfCity:false,
		queryParams: {
			city: '',
			province: '',
			beginDate: '',
			divisionCode:''
		},
		navCurrent: 0,
		navs: [
			{
				name: '当前',
				data: [] as IParty[],
				firstLoad: false
			},
			{
				name: '历史',
				data: [] as IParty[],
				firstLoad: false
			}
		],
	})

	onLoad((options : any) => {
		wx.showShareMenu({
			withShareTicket: true,
			menus: ['shareAppMessage', 'shareTimeline']
		})
		if (options.where) {
			d.queryParams = JSON.parse(decodeURIComponent(options.where));
		} else {
			d.queryParams = {
				province: '全部',
				city: '全部',
				beginDate: '',
				divisionCode:''
			}
		}
	})

	onMounted(() => {
		watch(() => d.navCurrent, () => {
			load()
		}, { immediate: true })
		watch(() => d.onlyViewSelfCity, () => {
			load()
		})
	})
	
	function swiperChanged(e : any) {
		d.navCurrent = e.detail.current
	}


	function getQueryParams(pageIndex : number, pageSize : number, isHistory : boolean) {
		const now = formatDate(new Date(), "yyyy/MM/dd 00:00")
		const spWhere = {
			pageIndex: pageIndex,
			pageSize: pageSize,
		} as any;
		if(d.onlyViewSelfCity){
			spWhere.divisionCode = user.tryLocal.divisionCode;
		}
		for (const k in spWhere) {
			if (spWhere[k] == "全部" || spWhere[k] === "") {
				delete spWhere[k]
			}
		}
		if (isHistory) {
			//当前时间为结束时间
			spWhere.dateEnd = now
		}
		else {
			//当前时间为开始时间
			spWhere.dateStart = now
		}
		return spWhere
	}

	function load(pageNum : number = 1, pageSize : number = 15, isHistory ?: boolean) {

		isHistory = d.navCurrent == 1;
		ajax.get<{ list : IParty[] }>('/party', getQueryParams(pageNum, pageSize, isHistory)).then(res => {
			let list = res.list.map(v => parseItem(v));
			if(pageNum==1){
				refs.value[d.navCurrent].clear()
			}
			refs.value[d.navCurrent].complete(list);

			
		})
		.catch((err : any) => {
			refs.value[d.navCurrent].complete(false);
		})

	}
	
	function parseItem(item : IParty) {

		if (user.currentLocal.lat) {
			item.distance = getDistance(user.currentLocal.lat, user.currentLocal.lon, item.lat, item.lon).toFixed(1) + '公里';
		}
		//item.images = (item.images || "").split(',')
		//转到solt item里时间变为string了
		// item.beginDate = Date.myParse(item.beginDate)

		return item;
	}

	function goEdit(item : IParty) {
		uni.navigateTo({
			url: "PartyEdit?id=" + item.id
		})
	}
	function goView(item : IParty) {
		uni.navigateTo({
			url: "PartyView?id=" + item.id
		})
	}
</script>

<style lang="scss">
	.viewSelfCityCheckbox{
		
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 13px;
		.wx-checkbox-input,
		.uni-checkbox-input {
			border-radius: 100upx;
			border: 2px solid $uni-border-color;
			width: 12px;
			height: 12px;
		}
		&[checked]{
			color: $uni-color-success;
		.wx-checkbox-input,
		.uni-checkbox-input {
			//border: 2px solid $uni-color-success;
		}
		}
	}
	.swiper-scroll {
		height: 100%;
		padding: 0 12px 0 12px;
		swiper {
			height: 100%;
			margin-right: -10px;
		}
	}
	.item-box {
		padding: 5px 0px;
		padding-right: 12px;
	}
</style>