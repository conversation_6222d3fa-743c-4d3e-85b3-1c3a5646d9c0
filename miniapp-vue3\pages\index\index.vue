<template>

	<view class="page-container" style="padding-bottom:10px;">
			<my-page-header title="毽球助手" />
		<my-user-bar />
		<view class="mpt10">
			<my-card-city />
		</view>

		<view class="mpt10">
			<my-card-week-party></my-card-week-party>
		</view>
		
<!-- 		<view class="flex flex-between flex-center">
			<view class="">
				我的约毽记录
			</view>
			<view class="link f-lg">
				查看
			</view>
		</view> -->
		
		<view style="margin-top: 20px;">
			<my-card-venue />
		</view>

		<view style="margin-top: 20px;">
			<my-card-cteam />
		</view>
		
<!-- 		<view class="card p1-box">
			<view class="title" style="position: relative;z-index: 2; letter-spacing: 2px;">
				欢迎您的加入
			</view>
		</view> -->

	</view>
</template>

<script lang="ts" setup>
	
	
</script>


<style lang="scss" scoped>
	.p1-box{
		display: flex;
		justify-content: center;
		align-items: center;
		height: 80px;
		position: relative;
		margin-bottom: 20px;
		&:before{
			content: "";
			display: block;
			position: absolute;
			top:0;
			left: 0;
			width: 100%;
			height: 100%;
			background-image: url(../../static/images/p1.jpg);
			background-position: top right;
			background-repeat: no-repeat;
			background-size: 60%; 
			filter: grayscale(1);
		}
		&::after{
			content: "";
			display: block;
			position: absolute;
			top:0;
			left: 0;
			width: 100%;
			height: 100%;
			background: linear-gradient(90deg,#2A2F37 50%,transparent 100%);
			z-index: 1;
		}
	}

</style>