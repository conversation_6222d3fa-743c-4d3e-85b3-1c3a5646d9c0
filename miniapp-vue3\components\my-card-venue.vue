<template>
	<view class="venue-card" style="gap: 10px;">
		<view style="line-height: 1.6;">
			<view class="f-color-gray">
				Venue
			</view>
			<view class="f-lg" >
				场地
			</view>
			<view class="btn cy-right-arrow "/>
		</view>
		<view class="venue-list">

			<view v-for="item,idx in list" :key="item.id">

				<view class="item style1" :style="{backgroundImage:`url(${item.imageArr[0]})`}">
					<view class="title">
						{{item.name}}
					</view>
					<view class="flex" style="justify-content: flex-end;">
						<my-rate :readonly="true" :value="item.star" :max="5" color="rgba(255,255,255,.5)" active-color="#ff9d00" :size="20" />
					</view>
				</view>

		</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from 'vue';
import { userStore } from '../store/user';
import ajax from '../common/ajax';
import { DefaultVenueCover } from '../common/config';

const list = ref<(IVenue&{cover:string,imageArr:string[]})[]>([])
const isLoaded = ref(false)
const us = userStore();
watch(()=>us.tryLocal.divisionCode,(n,o)=>{
	if(n&& n!=o){
		load();
	}
		
},{immediate:true})

async function load(){
	const res = await ajax.get<IVenue[]>('/venue/top',{
		divisionCode:us.tryLocal.divisionCode,
		top:3,
		isRandom:true
	});
	

	list.value = res.map(v=>{
		let imageArr = (v.images||"").split(",");
		var x=  {
			...v,
			cover:imageArr[0],
			imageArr
		};
		return x;
	});


}
</script>

<style lang="scss" scoped>

.venue-card{
	display: flex;
	justify-content: space-between;
	border-radius: 12px;
		//background-color: $uni-bg-color-grey;
	//background: linear-gradient(-90deg, rgba(42, 47, 55, 1),rgba(42, 47, 55, 0) 100%);
}
.venue-list{
	flex: 100%;
	display: flex;
	flex-direction: column;
	gap: 10px;
	.item{
		padding: 15px 10px 10px 10px;
		background-size: cover;
		background-position: center center;
		height: 80px;
		position: relative;
		background-repeat: no-repeat;
		// border:2px solid $uni-bg-color-grey;;
		background-color: $uni-bg-color-grey;
		border-radius: 5px;
		overflow: hidden;
		&.style1{
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			// box-shadow: 1px 1px 1px #000;
			&:before{
				content: "";
				position: absolute;
				width: 100%;
				height: 100%;
				left: 0px;
				top: 0px;
				display: block;
				background:linear-gradient(-90deg,$uni-bg-color-grey 30%,transparent 100%);
				z-index: 0;
				border-radius: 5px;
			}
			// &:after{
			// 	content: "";
			// 	position: absolute;
			// 	width: 100%;
			// 	height: 100%;
			// 	left: 0px;
			// 	top: 0px;
			// 	display: block;
			// 	// border:1px solid #ffffff55;
			// 	z-index: 2;
			// 	border-radius: 5px;
			// }	
		}
		&.style2{

			//background-color: $uni-bg-color-grey;
			display: flex;
			.img{
				width: 100px;height: 60px;
			}
			.title{
				font-size: $uni-font-size-base;
			}
			// background:linear-gradient(90deg,#2a2f37cc 0%,transparent 50%, #2a2f37cc 100%);
		}
		view{
			position: relative;
			z-index: 1;
			
		}
		.title{
			font-size: $uni-font-size-lg;
			text-shadow: 0 0 2px black;
			letter-spacing: 1px;
			position: relative;
			z-index: 1;
			font-weight: 400;
			
			width: 100%;
			  display: -webkit-box; /* 设置为WebKit内核的弹性盒子模型 */
			  -webkit-box-orient: vertical; /* 垂直排列 */
			  -webkit-line-clamp: 2; /* 限制显示两行 */
			  overflow: hidden; /* 隐藏超出范围的内容 */
			  text-overflow: ellipsis; /* 使用省略号 */
		}
	}
	
}
</style>