<template>
	<view class="body leaguePage" :style="{'background-image':$asset('bg','bg')}">
		<sheader :title="league.name ||''"></sheader>
		<view class="league-state" :class="{active:league.state===1}">
			<view class="iconfont iconleague2" ></view>
			<view class="txt">[{{enums.state[league.state]}}]</view>
		</view>
		<view class="league-time">
			<view class="iconfont icontime"></view> {{league.beginDate | dateFormat}} - {{league.endDate | dateFormat}}
		</view>
		<view class="league-intro">
			<view class="txt">
				<text>{{league.intro  ||""}}</text>
			</view>
		</view>
		<view class="matchs">
			<navigator v-for="(item,idx) in matchs" :key="idx" :url="'../NewLeagueMatch/NewLeagueMatch?id='+item.id" open-type="navigate" hover-class="navigator-hover">
			<view class="match">
				<text class="iconfont icon-arrow-left arrowEnter" ></text>
				<view class="state" :class="{active:item.state===1}">
						<view class="iconfont iconmatch" ></view>
						<view class="txt">{{enums.state[item.state]}}</view>
					</view>
				{{item.name}}
				<view class="teams">
					<view class="team"  v-for="(t,idx2) in item.MatchTeamResults" :key="idx2">
						
						<view class="teamName">
							{{t.Team.name}}
						</view>
						<view class="socre">
							{{t.score}}
						</view>
					</view>
				</view>
			</view>
			</navigator>
		</view>
	</view>
</template>

<script>
	export default {
	computed:{
			enums(){
				return this.$store.getters.enums
			},
	},
		data() {
			return {
				league:{
					id:'',
					state:0
				},
				matchs:[]
			};
		},
		created(){
			
		},
		async onLoad(param){

			//this.load()
		},
		onShow(){
			this.load()
		},
		methods:{
			async load(){
				let league = await this.$ajax.get('/newleague');
				let matchs = await this.$ajax.get('/newleague/matchs')
				this.league = league.base;
				this.matchs = matchs;
			},
		}
	}
</script>

