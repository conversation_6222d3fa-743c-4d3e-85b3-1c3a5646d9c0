{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"title":"",
			"style": {
				"navigationBarTitleText": "uni-app",
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/regMember/regMember",
			"style" : 
			{
				"navigationBarTitleText" : "注册会员"
			}
		},
		{
			"path" : "pages/my/my",
			"style" : 
			{
				"navigationBarTitleText" : "我的"
			}
		},
		{
			"path" : "pages/party/list",
			"style" : 
			{
				"navigationBarTitleText" : "约毽",
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/venue/list",
			"style" : 
			{
				"navigationBarTitleText" : "球场"
			}
		},
		{
			"path" : "pages/venue/edit",
			"style" : 
			{
				"navigationBarTitleText" : "球场编辑"
			}
		},
		{
			"path" : "pages/venue/view",
			"style" : 
			{
				"navigationBarTitleText" : "球场详情"
			}
		},
		{
			"path" : "pages/league/list",
			"style" : 
			{
				"navigationBarTitleText" : "赛事"
			}
		},
		{
			"path" : "pages/league/view",
			"style" : 
			{
				"navigationBarTitleText" : "赛事详情"
			}
		},
		{
			"path" : "pages/match/match",
			"style" : 
			{
				"navigationBarTitleText" : "比赛"
			}
		},
		{
			"path" : "pages/cTeam/list",
			"style" : 
			{
				"navigationBarTitleText" : "团队"
			}
		},
		{
			"path" : "pages/party/partyEdit",
			"style" : 
			{
				"navigationBarTitleText" : "创建约毽",
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/party/partyView",
			"style" : 
			{
				"navigationBarTitleText" : "约毽",
				"navigationStyle": "custom"
			}
		},
		{
			"path" : "pages/party/select-venue",
			"style" : 
			{
				"navigationBarTitleText" : "选择场地"
			}
		},
		{
			"path" : "pages/comment/comment",
			"style" : 
			{
				"navigationBarTitleText" : "评论"
			}
		}
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uni-app",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8",
		"navigationStyle": "custom"
		
	},
	"tabBar": {
		"backgroundColor": "#303640",
		"color": "#cdcdcd",
		"selectedColor": "#E0FE10",
		"fontSize": "24px",
		"iconfontSrc": "https://fonts.cdnfonts.com/s/66596/OPPOSansMedium.woff",
		"list": [
			{
				"text": "首页",
				"pagePath": "pages/index/index",
				"iconPath": "static/tabs/home1.png",
				"selectedIconPath": "static/tabs/home.png"
			},
			{
				"text": "约毽",
				"pagePath": "pages/party/list",
				"iconPath": "/static/tabs/party1.png",
				"selectedIconPath": "/static/tabs/party.png"
			},
			{
				"text": "场地",
				"pagePath": "pages/venue/list",
				"iconPath": "/static/tabs/venue1.png",
				"selectedIconPath": "/static/tabs/venue.png"
			},
			{
				"text": "赛事",
				"pagePath": "pages/league/list",
				"iconPath": "/static/tabs/league1.png",
				"selectedIconPath": "/static/tabs/league.png"
			},
			{
				"text": "我的",
				"pagePath": "pages/my/my",
				"iconPath": "/static/tabs/my1.png",
				"selectedIconPath": "/static/tabs/my.png"
			}
		]
	},
	"uniIdRouter": {},
	"easycom": {
	  "autoscan": true, //是否自动扫描组件
	  "custom": {
	    "^my-(.*)": "@/components/my-$1.vue" // 匹配components目录内的vue文件
		// "^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue"
		
	  }
	}
}
