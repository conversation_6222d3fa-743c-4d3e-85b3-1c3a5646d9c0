<template>
	<view class="">
		<view class="row-flex-around" style="margin-top: 20rpx;align-items: flex-start;">
			<view class="iconfont iconaddMsg auto" style="font-size: 60rpx;color: rgba(255,255,255,.5);"></view>
			<view :class="{'comment-hidden':d.inputHidden}" style="position: relative;width: 100%;">
				<textarea :disable-default-padding="true" confirm-type="send" @focus="d.inputHidden=false"
					:maxlength="d.limit" class="my-input  comment-input" v-model="d.model.content"
					placeholder="评论"></textarea>
				<view class="comment-submit" @tap="saveComment"
					:class="{'g-disabled':d.model.content.length>d.limit || d.doing}">
					<text class="f-mini">{{d.model.content.length}}/{{d.limit}}</text>
					<view class="iconfont iconok auto ">
					</view>
				</view>
			</view>
		</view>
		<view class="my-box light f-sm" style="margin-top: 20rpx;">
			<view style="text-align: right;" @tap="load">
				查看留言>>>
			</view>
			<view class="item line-1" v-for="(item, idx) in d.listTop" :key="idx">
				<view class="">
					<image class="avatar-icon mini" style="margin-right: 10rpx;" :src="item.user.avatar"
						@tap="goUser(item.user.id)" />
					<text style="padding-right: 20rpx;">{{ item.user.nickName }}</text><text
						class="f-time">{{item.updatedAt}}</text>
				</view>
				<view class="comment-content">{{ item.content  }}</view>
			</view>
			<uniPopup type="center" ref="popRef" :animation="false">
				<view style="height: 70vh;width: 80vw;background: rgba(255,255,255,1);padding: 20rpx;">
<!-- 					<z-paging ref="paging" v-model="d.list" @query="load()">
						<view class="item" v-for="(item,index) in d.list" :key="index">
							<view class="item-title">{{item.title}}</view>
						</view>
					</z-paging> -->
					<my-list-async :pageSize="15" :autoLoad="false" style="height: 100%;" :url="`/comments/${targetId}`" ref="ml">
						<template #item="{ item, _index, scope }">
							<view class="item line-2" >
								<view class="" >
									<image class="avatar-icon mini" style="margin-right: 10rpx;" :src="item.user.avatar" @tap="goUser(item.user.id)"/>
									<text style="padding-right: 20rpx;">{{ item.user.nickName }}</text> <text class="f-time">{{item.updatedAt }}</text>
								</view>
								<view class="comment-content">{{ item.content }}</view>
							</view>
						</template>
					</my-list-async>
				</view>
			</uniPopup>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import { reactive, ref, watch } from 'vue';
	import { appStore } from '../store';
	import ajax from '../common/ajax';
	import { wxContentCheck } from '../common/funs';
	const app = appStore();
	const props = defineProps(['targetId'])
	const paging = ref();
	watch(() => props.targetId, () => {
		loadTop();
	})
	const d = reactive({
		listTop: [] as any[],
		list:[] as any[],
		limit: 256,
		inputHidden: true,
		doing: false,
		model: {
			content: ''
		}
	})

	async function loadTop() {
		d.listTop = await ajax.get<any>(`/comment/list?targetId:${props.targetId}`, { pageSize: 5 })
	}
	async function load(pageIndex=0,pageSize:number=10) {
		var res = await ajax.get<any>(`/comment/list?targetId:${props.targetId}`, { pageSize,pageIndex })
		paging.value.complete(res.list);

	}
	function goUser(id : string) {
		uni.navigateTo({
			url: "/pages/User/User?id=" + id
		})
	}
	async function saveComment() {

		d.doing = true;
		try {
			await wxContentCheck(d.model.content, 'txt');
			await ajax.post('/comment', { targetId: props.targetId, content: d.model.content });
			d.model.content = "";
			d.inputHidden = false;
			setTimeout(() => {
				uni.showToast({
					title: "提交成功"
				})
			}, 200)

			loadTop();
		} finally {
			d.doing = false;
		}
	}
</script>

<style lang="scss">
	.comment-input {
		width: 100%;
		height: 160rpx;
		transition: height .3s;
		margin-top: 10rpx;
	}

	.item {
		padding: 10rpx 0;
	}

	.comment-content {
		padding-left: 60rpx;
	}

	.comment-submit {
		letter-spacing: 2rpx;
		background: rgba(0, 0, 0, .2);
		color: #fff;
		padding: 0 10rpx;
		position: absolute;
		bottom: 10rpx;
		right: 10rpx;
		z-index: 10;
		transition: opacity .3s;
	}

	.comment-hidden {
		.comment-input {
			height: 60rpx;
		}

		.comment-submit {
			opacity: 0;
			pointer-events: none;
		}
	}
</style>