<template>
	<view class="page-imgbg container" style="height: 100vh;" :style="{'background-image':$asset('yellow2','bg')}">
		<myListAsync :transf="transfDate" :pageSize="15" style="height: 100%;" url="/my/visits" ref="ml">
			<view class="line-1" style="margin-bottom: 20rpx;background: rgba(255,255,255,.1);padding: 10rpx;" slot="item" slot-scope="{ item, _index, scope }">
				<view class="item">
					<view class="item-date">
						<text class="date-month">{{ item.month }}月</text> 
						<view class="date-day">{{ item.day }}</view>
					</view>
					<view class="item-content">
						<text class="my-tag light item-time">{{ item.time }}分</text> 
						<view class="f-color-dark" style="padding: 10rpx;">
							拜访了 『 <text class="f-big f-color-dark f-strong">{{ item.name }}</text> 』
						</view>
					</view>
				</view>
			</view>
		</myListAsync>
	</view>
</template>

<script lang="ts" setup>
import { formatDate } from '../../common/funs'

function transfDate(v: any) {
	if (!v.month) {
		const s = formatDate(v.updatedAt, 'MM,dd,hh:mm').split(",")
		v.month = s[0]
		v.day = s[1]
		v.time = s[2]
	}
	return v
}
</script>

<style lang="scss">
	page{
		background-color:$uni-color-warning;
	}
	.item{
		display: flex;align-items: flex-start;
	}
	.item-time{
		color: $uni-color-primary !important;
	}
	.item-date{
		color: #FFFFFF;
		width: 80rpx;
	}
	.item-content{
		flex: 100%;
		margin-left: 10rpx;
		padding:0rpx 10rpx 10rpx 10rpx;
	}
	.date-month{
		font-size: .8em;
	}
	.date-day{
		font-size: 1.5em;
		font-weight: bold;
	}
	.post-img{
		width:100rpx;
		height: 100rpx;
		margin: 10rpx 10rpx 0rpx 0;
		border-radius: 4rpx;
	}
</style>
