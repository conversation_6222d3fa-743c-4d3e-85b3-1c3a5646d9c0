<template>
	<view class="body" :style="{'background-image':$asset('bg','bg')}">
		<my-page-header title="参赛队详情" :showBack="true" />
		<view class="body-flex">
			<scroll-view class="teamPage" :scroll-y="true" style="height: calc(100vh - 280rpx);">
				<view class="title">
					{{ d.data.name }}
				</view>

				<view class="team-info">
					{{ d.data.intro || "" }}
				</view>
				<view class="team-avatar">
					<image :src="d.data.avatar"></image>
				</view>
				<view class="team-content">
					{{ d.data.intro || "" }}
					{{ d.data.intro || "" }}
					{{ d.data.intro || "" }}
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script lang="ts" setup>
import { reactive } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import ajax from '../../common/ajax'

const d = reactive({
	data: {
		name: "",
		intro: '',
		avatar: ''
	}
})

onLoad((param: any) => {
	load(param.id)
})

async function load(id: string) {
	try {
		const res = await ajax.get<any>(`/team/${id}`)
		d.data = res
	} catch (e) {
		console.error('加载队伍信息失败', e)
	}
}
</script>

<style lang="scss" scoped>
body{
	overflow: hidden;
}
.title{
	font-size: 110%;
	text-align: center;
	height: 50rpx;
}
.teamPage{
	margin-top: -40rpx;
}
.team-venue{
	margin: 20rpx;
	text-align: center;
	color: #4CD964;
	font-size: 90%;
}
.team-avatar{
	text-align: center;
}
.team-content{
	font-size: 80%;
	padding: 30rpx;
	color:rgba(255,255,255,.7);
}
.team-info{
	font-size: 80%;
	line-height: 120%;
	position: relative;
	box-shadow: 8rpx 8rpx 0 rgba(0,0,0,.2);
	background:
		linear-gradient(-198.5deg, transparent 20px,rgba(254,252,248,.15) 0,rgba(232,179,81,.15) 100%),
	;
	padding: 20rpx 20rpx;
	margin: 24rpx;
	padding-left:80rpx;
	color:rgba(255,255,255,.6);

	.name{
		color: #e6ab3d;
		font-weight: 500;
		letter-spacing: 2rpx;
		margin-bottom: 10rpx;
	}
	.item{
		line-height:42rpx;
		opacity: .5;
		color:#fff;
		font-size:80%;
		text-overflow:ellipsis;overflow:hidden;
		white-space:nowrap; 
		font-weight: 100;
		width:550rpx;
		.iconfont{
			margin-right: 20rpx;
			display: inline-block;
		}
	}

	&.active{
	background:
		linear-gradient(-198.5deg, transparent 20px,rgba(254,252,248,.15) 0,rgba(255,153,0,.8) 100%),
		 linear-gradient(0,rgba(255,255,255,.4) 1px,transparent 0),
		 linear-gradient(270deg,rgba(255,255,255,.4) 1px,transparent 0),
	;
	.state{
		color: #ff9900;
		.iconfont{
			color: #ff9900;
		}
	}
	}
}
</style>
