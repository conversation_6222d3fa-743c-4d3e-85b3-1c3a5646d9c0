<template>
	<scroll-view class="list-scroll-content" :class="cls" scroll-y 
		:scroll-top="top"
		@scrolltolower="scrolltolower"  
		@scrolltoupper="scrolltoupper" 
		@catchtouchmove="touchmove"
		@touchstart="touchstart" 
		@touchmove.stop.prevent="touchmove" 
		@touchend="touchend"
		>
		<view class="refresh-flag uni-load-more" :class="{close:downloadRefeshClosding}" :style="{height:(80*downloadRefeshPer+'rpx')}" id="refreshBar">
			<view class="uni-load-more__img" v-if="downStatus2 === 'loading'">
				<view class="load1">
					<view ></view>
					<view ></view>
					<view ></view>
					<view ></view>
				</view>
				<view class="load2">
					<view ></view>
					<view ></view>
					<view ></view>
					<view ></view>
				</view>
				<view class="load3">
					<view ></view>
					<view ></view>
					<view ></view>
					<view ></view>
				</view>
			</view>
			<text class="uni-load-more__text">{{downStatus2=='loading'?'刷新数据中...':'下拉刷新'}}</text>
		</view>
		

		<slot></slot>

		
		<view class="uni-load-more" v-if="upStatus2!='-1'">
			<view class="uni-load-more__img" v-if="upStatus2 === 'loading'">
				<view class="load1">
					<view ></view>
					<view ></view>
					<view ></view>
					<view ></view>
				</view>
				<view class="load2">
					<view ></view>
					<view ></view>
					<view ></view>
					<view ></view>
				</view>
				<view class="load3">
					<view ></view>
					<view ></view>
					<view ></view>
					<view ></view>
				</view>
			</view>
			
			<text class="uni-load-more__text" >{{upStatus2 === 'more' ? '上拉显示更多' : (upStatus === 'loading' ? '正在加载...' : '---')}}</text>
		</view>
	</scroll-view>
</template>

<script>

	export default {
		data(){
			return{
				downloadRefeshPer:0,
				downloadRefeshClosding:false,
				refreshi:false,
				ra:'',//动画
				touchstart_Y:0,
				upStatus2:'-1',
				downStatus2:'-1',
				top:-1
			}
		},
		props:['cls','upStatus','downStatus'],
		watch:{
			downStatus(v){
				if(v==""){
					
					setTimeout(()=>{
						this.downloadRefeshPer=0;
						this.downloadRefeshClosding = true;
						setTimeout(()=>{
							this.downloadRefeshClosding = true;
							this.downStatus2 = v;
						},350)
					},500)

				}else{
					
					this.downStatus2 = v;
				}
			},
			upStatus(v){
				if(v==""){
					
					setTimeout(()=>{


							this.upStatus2 = v;

					},500)
				
				}else{
					
					this.upStatus2 = v;
				}
			}
		},
		methods:{
			toTop(){
				console.log('置顶')
				this.top=0;
				setTimeout(()=>{this.top=-1},500) //触发下次变更
			},
			scrolltolower(){
				this.$emit('overBottom')
				//有时间再实现松手触发，交互效果更好
			},
			scrolltoupper(){
				console.log('到顶')
			},
			touchstart(e){
				console.log('touchstart')
				this.touchstart_Y = e.touches[0].pageY;
				e.stopPropagation();
				e.preventDefault();
				const query = this.createSelectorQuery()
				let r = query.select('#refreshBar')
			},
			touchmove(e){
				
				let p = (e.touches[0].pageY - this.touchstart_Y)/80;
				this.downloadRefeshPer = p>1?1:p<0?0:p;
				e.stopPropagation();
				e.preventDefault();
			},
			touchend(e){
				console.log('touchend')
				if(this.downloadRefeshPer==1){
					this.$emit('overTop')
				}else{
					this.downloadRefeshPer = 0;
					this.downloadRefeshClosding = true;
					setTimeout(()=>{
						this.downloadRefeshClosding = false;
					},350)
				}
				e.stopPropagation();
				e.preventDefault();
			},
			//下拉刷新
			refresh(){
				console.log('下拉刷新')
			},			
		}
	}
</script>

<style lang="scss" >
.list-scroll-content{
	height: 100%;
	-webkit-overflow-scrolling: touch;
}
.refresh-flag{
	height: 80upx;
	line-height: 80upx;
	overflow: hidden;
	-webkit-overflow-scrolling: touch;
	position: relative;
	//transition:height .1s;
	&.close{
		transition:height .3s;
	}
}

.uni-load-more {
		display: flex;
		flex-direction: row;
		height: 80upx;
		align-items: center;
		justify-content: center
	}

	.uni-load-more__text {
		font-size: $uni-font-size-sm;
		color: $uni-text-color-grey;
		opacity: .5;
	}

	.uni-load-more__img {
		height: 24px;
		width: 24px;
		margin-right: 10px
	}

	.uni-load-more__img>view {
		position: absolute
	}

	.uni-load-more__img>view view {
		width: 6px;
		height: 2px;
		border-top-left-radius: 1px;
		border-bottom-left-radius: 1px;
		background: #999;
		position: absolute;
		opacity: .2;
		transform-origin: 50%;
		animation: load 1.56s ease infinite
	}

	.uni-load-more__img>view view:nth-child(1) {
		transform: rotate(90deg);
		top: 2px;
		left: 9px
	}

	.uni-load-more__img>view view:nth-child(2) {
		transform: rotate(180deg);
		top: 11px;
		right: 0
	}

	.uni-load-more__img>view view:nth-child(3) {
		transform: rotate(270deg);
		bottom: 2px;
		left: 9px
	}

	.uni-load-more__img>view view:nth-child(4) {
		top: 11px;
		left: 0
	}

	.load1,
	.load2,
	.load3 {
		height: 24px;
		width: 24px
	}

	.load2 {
		transform: rotate(30deg)
	}

	.load3 {
		transform: rotate(60deg)
	}

	.load1 view:nth-child(1) {
		animation-delay: 0s
	}

	.load2 view:nth-child(1) {
		animation-delay: .13s
	}

	.load3 view:nth-child(1) {
		animation-delay: .26s
	}

	.load1 view:nth-child(2) {
		animation-delay: .39s
	}

	.load2 view:nth-child(2) {
		animation-delay: .52s
	}

	.load3 view:nth-child(2) {
		animation-delay: .65s
	}

	.load1 view:nth-child(3) {
		animation-delay: .78s
	}

	.load2 view:nth-child(3) {
		animation-delay: .91s
	}

	.load3 view:nth-child(3) {
		animation-delay: 1.04s
	}

	.load1 view:nth-child(4) {
		animation-delay: 1.17s
	}

	.load2 view:nth-child(4) {
		animation-delay: 1.3s
	}

	.load3 view:nth-child(4) {
		animation-delay: 1.43s
	}

	@-webkit-keyframes load {
		0% {
			opacity: 1
		}

		100% {
			opacity: .2
		}
	}
</style>
