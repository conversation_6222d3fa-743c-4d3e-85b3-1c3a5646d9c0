<template>
	<view class="page-imgbg container" style="height: 100vh;" :style="{'background-image':$asset('yellow2','bg')}">
		<view style="text-align: right; line-height: 100rpx;" class="row-flex-justify">
			<view class="">
				共计 {{ d.datas.total }} / 24 张 
			</view>
			<button class="my-button" :class="{success: d.modify}" @tap="d.modify = !d.modify">{{ d.modify ? '关闭' : '编辑' }}</button>
		</view>
		<myListAsync @changed="updateList" :scope="{modify: d.modify}" :pageSize="15" style="height:calc(height:100vh - 50px)" url="/my/gallery" ref="ml">
			<button slot="beforSlot" class="addBtn" slot-scope="{scope}" v-show="scope.modify" @tap="selectImg()">
				<view class="iconfont iconadd"></view>
			</button>
			<view class="item" :class="{del: item.isDel}" slot="item" slot-scope="{ item, _index, scope }">
				<image lazy-load class="img" mode="aspectFill" lazy-load :src="item.url + '?imageView2/1/w/220/h/220'" @tap="viewImage(_index)"></image>
				<view v-show="scope.modify" class="removeBtn g-hover" @tap="del(_index)">X</view>
			</view>
		</myListAsync>
		
		<uploadImages ref="uploadImg" @uploaded="addImgData" :show="false" provider="qn" path="user" :max="1" v-model="d.model.images"></uploadImages>
	</view>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue'
import ajax from '../../common/ajax'
import uploadImages from "@/components/upload-images.vue"

const ml = ref()
const uploadImg = ref()

const d = reactive({
	modify: false,
	datas: {
		list: [],
		total: 0
	},
	model: {
		images: []
	}
})

function viewImage(idx = 0) {
	const imgs = ml.value.datas.list.map((v: any) => v.url)
	wx.previewImage({
		urls: imgs,
		current: imgs[idx]
	})
}

function updateList(data: any) {
	d.datas = data
}

function selectImg() {
	if (d.datas.total < 24) {
		uploadImg.value.select()
	} else {
		uni.showToast({
			title: '图片数据已经超出限制',
			icon: 'none'
		})
	}
}

async function addImgData(imgModel: any) {
	try {
		const res = await ajax.post('/my/gallery', { imageUrls: [imgModel.filePath] })
		ml.value.addList(res)
	} catch (e) {
		console.error('添加图片失败', e)
	}
}

async function del(idx: number) {
	const [err, res] = await uni.showModal({
		content: '确认删除？'
	})
	if (res.confirm) {
		try {
			const item = ml.value.datas.list[idx]
			await ajax.post('/my/gallery/del', { imageIds: [item.id] })
			ml.value.remove(idx)
		} catch (e) {
			console.error('删除图片失败', e)
		}
	}
}
</script>

<style lang="scss">
	page{
		background-color:$uni-color-warning;
	}
	.item{
		display: inline-block;
		position: relative;
		padding: 10px;
	}
	::v-deep .del{
		filter: grayscale(1);
		opacity: .3s;
		pointer-events: none;
		.removeBtn{
			display: none;
		}
	}
	.img{
		width: 200rpx;
		height: 200rpx;
		margin: 0rpx;
		border: 1rpx solid rgba($color: #fff, $alpha: 1);
		border-radius: 4rpx;
		display: inline-block;
		box-shadow: 0rpx 0rpx 8rpx rgba($color: #853a05, $alpha: .1);
	}
	.removeBtn{
		width: 50rpx;
		height: 50rpx;
		border-radius: 50rpx;
		border:2px solid #fff;
		color: #fff;
		background: #000;
		text-align: center;
		line-height: 50rpx;
		font-weight: bold;
		position: absolute;
		top: 0rpx;
		right: 0rpx;
	}
	.addBtn{
		width: 200rpx;
		height: 200rpx;
		margin: 10rpx;
		background: rgba($color: #fff, $alpha: 1);
		color: $font-color-dark;
		.iconfont{
			font-size: 100rpx;
			line-height: 2;
		}
	}
</style>
