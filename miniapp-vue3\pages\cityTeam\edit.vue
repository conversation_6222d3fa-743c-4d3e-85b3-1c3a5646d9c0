<template>
  <view class="page-container">
    <my-page-header :title="isEdit ? '编辑团队' : '新建团队'" :showBack="true" />

    <view class="form-card">
      <view style="transform: scale(.8);" >
          <my-upload-images cls="cy" v-model="avatarArr" :max="1" @change="onAvatarChange" />

      </view>
	  <view class="g-center ">
	    团队头像
	  </view>
      <my-cell label="团队名称" labelCls="label-require" line="2">
        <template #rightSlot>
          <input class="my-input" placeholder="请输入团队名称" type="text" v-model="form.name" />
        </template>
      </my-cell>

      <my-cell label="团队简介" >
      </my-cell>
		<view style="padding-left: 10rpx;">
			      <textarea class="my-textarea" placeholder="一句话介绍团队" v-model="form.intro" style="height: 40px;" />
		</view>
      <my-cell label="团队类型" line="2">
        <template #rightSlot>
          <my-switch-binary cls="sm" :source="typeOptions" v-model="form.type" />
        </template>
      </my-cell>

      <my-cell label="团队规模" line="2">
        <template #rightSlot>
          <input class="my-input" type="number" placeholder="人数" v-model.number="form.size" />
        </template>
      </my-cell>

      <my-cell label="所在城市" line="2">
        <template #rightSlot>
          <my-select-city readonly v-model="form.divisionCode" @change="onCityChange" />
        </template>
      </my-cell>

      <my-cell label="详细地址" line="2">
        <template #rightSlot>
          <input class="my-input" placeholder="请输入详细地址" v-model="form.address" />
        </template>
      </my-cell>

      <map class="map" :longitude="form.lon" :latitude="form.lat"
        :markers="[{id:1,width:20,height:30,latitude:form.lat,longitude:form.lon,name:form.name}]"
        scale="15" style="width: 100%; height: 160px;" @tap="chooseLocation"></map>
      <my-cell label="地图位置" @rightClick="chooseLocation" right="点击选择" arrow=1 line="1"></my-cell>

      <my-cell label="经度" line="2">
        <template #rightSlot>
          <input class="my-input" type="number" placeholder="经度" v-model="form.lon" />
        </template>
      </my-cell>
      <my-cell label="纬度" line="2">
        <template #rightSlot>
          <input class="my-input" type="number" placeholder="纬度" v-model="form.lat" />
        </template>
      </my-cell>

      <my-cell label="负责人" line="2">
        <template #rightSlot>
          <input class="my-input" placeholder="负责人姓名" v-model="form.leader" />
        </template>
      </my-cell>
      <my-cell label="联系方式" line="2">
        <template #rightSlot>
          <input class="my-input" placeholder="手机号或微信号" v-model="form.leaderLink" />
        </template>
      </my-cell>

      <my-cell label="团队图片" line="2">
        <template #rightSlot>
          <my-upload-images v-model="imagesArr" :max="6" @change="onImagesChange" />
        </template>
      </my-cell>

      <my-cell label="备注">
        <template #rightSlot>
          <textarea class="my-textarea" placeholder="备注信息" v-model="form.bak" style="height: 60px;" />
        </template>
      </my-cell>
    </view>

    <view class="page-bottom">
      <button class="my-btn success" @click="submitForm">{{ isEdit ? '保存修改' : '创建团队' }}</button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import ajax from '../../common/ajax'
import { appStore } from '../../store'
import { getLocByLL } from '@/common/funs'

const app = appStore()
const isEdit = ref(false)
const form = reactive({
  id: '',
  name: '',
  intro: '',
  avatar: '',
  content: '',
  bak: '',
  type: 0,
  size: 0,
  country: '',
  province: '',
  city: '',
  district: '',
  divisionCode: 0,
  lat: 0,
  lon: 0,
  address: '',
  leader: '',
  leaderLink: '',
  images: '',
  sportId: '',
  createdUserId: '',
  createdUser: {
    id: '',
    avatar: '',
    nickName: ''
  },
  updatedAt: ''
})
const avatarArr = ref<string[]>([])
const imagesArr = ref<string[]>([])

// 团队类型选项
const typeOptions = app.enums?.cityTeamType || []

onLoad(async (params: any) => {
  if (params.id) {
    isEdit.value = true
    const detail = await ajax.get<any>(`/cityTeam/${params.id}`)
    Object.assign(form, detail)
    avatarArr.value = form.avatar ? [form.avatar] : []
    imagesArr.value = form.images ? form.images.split(',').filter(Boolean) : []
  }
})

// 头像上传回调
function onAvatarChange(arr: string[]) {
  avatarArr.value = arr
  form.avatar = arr[0] || ''
}

// 团队图片上传回调
function onImagesChange(arr: string[]) {
  imagesArr.value = arr
  form.images = arr.join(',')
}

// 城市选择回调
async function onCityChange(code: number, info: any) {
  form.divisionCode = code
  form.province = info.province
  form.city = info.city
  form.district = info.district
}

// 地图选点
async function chooseLocation() {
  const res = await uni.chooseLocation({
    latitude: form.lat,
    longitude: form.lon
  })
  if (!res.errMsg || res.errMsg === 'chooseLocation:ok') {
    form.lat = res.latitude
    form.lon = res.longitude
    form.address = res.address
    form.name = form.name || res.name
    const { city, province, divisionCode } = await getLocByLL(form.lat, form.lon)
	console.log(city,province)
    form.city = city
    form.province = province
    form.divisionCode = divisionCode
  }
}

// 提交表单
async function submitForm() {
  let error = ''
  if (!form.name) error = '团队名称不能为空'
  if (!form.address || !form.lon || !form.city) error = '请完善团队位置'
  if (!form.leader && !form.leaderLink) error = '负责人或联系方式必填一项'
  if (error) {
    uni.showToast({ icon: 'none', title: error })
    return
  }
  form.avatar = avatarArr.value[0] || ''
  form.images = imagesArr.value.join(',')
  try {
    if (isEdit.value) {
      await ajax.put(`/cityTeam/${form.id}`, form)
      uni.showToast({ title: '修改成功', icon: 'success' })
    } else {
      await ajax.post('/cityTeam', form)
      uni.showToast({ title: '创建成功', icon: 'success' })
    }
    setTimeout(() => uni.navigateBack(), 800)
  } catch (e) {
    uni.showToast({ title: '提交失败', icon: 'error' })
  }
}
</script>

<style lang="scss" scoped>
.cityTeam-edit-page {
  .form-card {
    background: #fff;
    border-radius: 20rpx;
    margin: 30rpx;
    padding: 30rpx;
  }
  .my-input {
    width: 220px;
    font-size: 1em;
    text-align: right;
    background: transparent;
    border: none;
    outline: none;
    color: #333;
  }
  .my-textarea {
    width: 220px;
    font-size: 1em;
    background: transparent;
    border: none;
    outline: none;
    color: #333;
    resize: none;
  }
  .map {
    margin: 20rpx 0;
    box-shadow: 0 0 15upx rgba(0,0,0,.1);
    background-color: #fff;
    border-radius: 10rpx;
  }
  .page-bottom {
    margin: 30rpx;
    margin-top: 0;
    display: flex;
    justify-content: flex-end;
    .my-btn {
      width: 100%;
      border-radius: 10rpx;
      font-size: 32rpx;
    }
  }
}
</style>