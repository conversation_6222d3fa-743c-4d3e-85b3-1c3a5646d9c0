<template>
	<view class="page-container">
		<my-page-header :title="d.match.league.name" :showBack="true" />
		<my-btn-refersh @ck="load" v-if="d.match.state === 1" />
		<view class="body matchPage">
			<view>
				<view class="match-state" :class="{active: d.match.state === 1}">
					<view class="iconfont iconmatch"></view>
					<view class="txt">比赛[{{ enums.state[d.match.state] }}]</view>
				</view>

				<view class="match-card">
					<view class="item-state" style="width: 60px;text-align: center;flex-shrink: 0;">
						<view class="iconfont icon-match f-lgx2"></view>
						<view class="">
							[{{ enums.state[d.match.state] }}]
						</view>
						
					</view>
					<view class="match-info">
						<view class="item f-color-success">
							{{ d.match.name || "" }}
						</view>
						<view class="item  flex">
							<view class="iconfont icon-time "></view>
							<view class="f-color-gray">
								{{ formatDate(d.match.beginDate, "yyyy/MM/dd hh:mm") }}
							</view>
			
						</view>
						<view class="item flex">
							<view class="iconfont icon-location"></view>
							<view class="f-color-gray">
								{{ d.match.address || "" }}
							</view>
							
						</view>
						<!-- 					<view class="item flex" v-if="d.match.intro || (d.match.league.name != d.match.intro)">
												<view class="iconfont icon-msg"></view>
												<view class="f-color-light">
													{{ d.match.intro || "" }}
												</view>
											</view> -->

						<view class="class">
							[ <text>{{ enums.matchType[d.match.type] }}</text> ]
							[ <text>{{ d.match.matchClass.name }}</text> ]
						</view>
					</view>

				</view>

				<view class="results">
					<view class="resultIdxType" style="pointer-events: none;display: none;"
						@click="d.idxType = (d.idxType === 1 ? 0 : 1)">
						{{ d.idxTypes[d.idxType] }}
						<text class="iconfont icondown"></text>
					</view>
					<view class="result" v-for="(item,idx) in resultByIdx" :key="idx">
						<view class="teamIdx" style="display: none;">{{ item.rank }}</view>
						<view class="teamName" @click="goTeam(item.team.id)">
							{{ item.team.name }}
							<view class="teamContent">
								{{ item.content || "" }}
							</view>
						</view>
						<view class="score">
							<text>{{ item.score }}</text>分
						</view>
						<view class="sign">
							<view class="scoreIdx"
								:class="[`iconfont icon-medal scoreIdx-${item.scoreIdx}`,{'icon-medal-123': item.scoreIdx < 4}]">
								<view class="idx" v-if="item.scoreIdx >3">
									{{ item.scoreIdx }}
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import { reactive, computed } from 'vue'
	import { onLoad, onShareAppMessage, onPullDownRefresh } from '@dcloudio/uni-app'
	import { appStore } from '../../store'
	import { userStore } from '../../store/user'
	import ajax from '../../common/ajax'
	import { formatDate } from '../../common/funs'

	const app = appStore()
	const user = userStore()
	const enums = app.enums

	const d = reactive({
		idxType: 1,
		idxTypes: {
			0: '队号排序',
			1: '比分排序'
		},
		id: '',
		match: {
			id: '',
			state: 0,
			name: '',
			beginDate: '',
			endDate: '',
			address: '',
			intro: '',
			type: 0,
			league: {
				id: '',
				name: ''
			},
			matchTeamResults: [] as any[],
			matchClass: {
				name: ''
			}
		}
	})

	const resultByIdx = computed(() => {
		let _results = [...d.match.matchTeamResults]
		_results.sort((a, b) => {
			if (a.score == b.score) {
				return parseInt(a.rank) > parseInt(b.rank) ? 1 : -1
			}
			return a.score >= b.score ? -1 : 1
		})
		_results.map((v, idx) => {
			v.scoreIdx = idx + 1
		})

		if (d.idxType == 0) {
			_results.sort((a, b) => {
				return parseInt(a.rank) > parseInt(b.rank) ? 1 : -1
			})
		}
		return _results
	})

	onLoad(async ({ id }) => {
		d.id = id
		await load()
	})

	onPullDownRefresh(() => {
		load()
	})

	onShareAppMessage(() => {
		const url = `/pages/match/match?id=${d.match.id}`
		return {
			title: `赛况比分:${d.match.name}`,
			path: url
		}
	})

	async function load() {
		try {
			const match = await ajax.get<any>(`/match/${d.id}`)
			computeTopScore(match.matchTeamResults)
			d.match = match
		} catch (e) {
			console.error('加载比赛信息失败', e)
		}
	}

	function goTeam(id : string) {
		uni.navigateTo({
			url: `/pages/Team/Team?id=${id}`
		})
	}

	function applyManager() {
		uni.showModal({
			title: '隐藏事件',
			content: '提交绑定赛事管理员申请',
			success: (res) => {
				if (res.confirm) {
					ajax.post('/applyManager', {
						openid: user.info.openid,
						nickName: user.info.nickName,
						avatar: user.info.avatar,
						type: 1, // AccountType.LeagueManager
						content: `赛季管理员:${d.match.league.name}:`,
						applyTarget: d.match.league.id
					}).then(() => {
						uni.showToast({
							title: '申请成功，等待审批对应账号'
						})
					})
				} else if (res.cancel) {
					uni.showToast({
						icon: 'none',
						title: '没事别乱点：）'
					})
				}
			}
		})
	}

	function goEdit() {
		uni.navigateTo({
			url: `/pages/match/matchResultEdit?id=${d.match.id}`
		})
	}

	function computeTopScore(results : any[]) {
		if (results.length < 2) return
		[...results].sort((a, b) => {
			return a.score >= b.score ? -1 : 1
		}).forEach((v, idx) => {
			v.scoreIdx = idx + 1
		})
	}
</script>

<style lang="scss" scoped>
	.matchPage {
		.match-card {
			padding: 18px;
			background: linear-gradient(-45deg, $uni-bg-color-grey 0, $uni-bg-color-hover 100%);
			border-radius: 12px;
			border: 1px solid #********;
			overflow: hidden;
			box-shadow: 4px 4px 4px #********;
			display: flex;
			justify-content: space-between;
			gap: 10px;

			.class {
				color: rgba(255, 255, 255, 0.8);
				position: absolute;
				right: 1rpx;
				top: 0rpx;
				background: rgba(0, 0, 0, 0.2);
				padding: 0rpx 10rpx;
				height: 50rpx;
				line-height: 50rpx;

				text {
					color: #6ad042;
					padding: 0 10rpx;
					font-size: 90%;
				}

				&::before {
					position: absolute;
					left: -50rpx;
					top: 0;
					content: '';
					height: 0;
					width: 0rpx;
					border: 50rpx solid transparent;
					border-top: 50rpx solid rgba(0, 0, 0, 0.2);
					border-right-width: 0rpx;
				}
			}

			.match-info{
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				gap: 10px;
			}
			.name {
				color: $uni-color-success;
				font-size: 32rpx;
				font-weight: 500;
				letter-spacing: 2rpx;
				margin-bottom: 10rpx;
			}

			.item {
				line-height: 42rpx;
				overflow: hidden;
				font-weight: 100;

				.iconfont {
					margin-right: 20rpx;
					display: inline-block;
					vertical-align: bottom;
				}
			}

			.txt {
				line-height: 36rpx;
				opacity: 0.5;
				color: #fff;
				font-size: 80%;
				text-overflow: ellipsis;
				height: 100%;
				font-weight: 100;

				.iconfont {
					margin-right: 20rpx;
					display: inline-block;
				}
			}
		}

		.results {
			border: 1px solid rgba($uni-bg-color-grey, 0.7);
			border-radius: 8px;
			background-color: rgba($uni-bg-color-grey, 0.3);
			margin-top: 30rpx;
			position: relative;
			padding: 15rpx;
			min-height: 100rpx;

		}

		//排名
		.resultIdxType {
			position: absolute;
			right: 80rpx;
			top: -28rpx;
			font-size: 20rpx;
			background: #42326e;
			padding: 4rpx 8rpx 4rpx 16rpx;
			letter-spacing: 2rpx;
			border: 1px solid rgba(255, 255, 255, 0.2);

			.iconfont {
				vertical-align: middle;
			}
		}

		.result {
			display: flex;
			align-items: top;
			margin-top: 15rpx;

			.teamName {
				padding-left: 10rpx;
				flex: 6;
				font-size: 90%;
				border-bottom: 1px solid rgba(255, 255, 255, 0.2);
				padding-top: 15rpx;

				.teamContent {
					padding-top: 10rpx;
					padding-bottom: 10rpx;
					color: rgba(255, 255, 255, 0.3);
					font-size: .9em;
				}
			}

			&:last-child {

				.teamName,
				.score {
					border-bottom: 0;
				}
			}

			.score {
				width: 140rpx;
				flex-grow: 0;
				text-align: right;
				border-bottom: 1px solid rgba(255, 255, 255, 0.2);
				position: relative;
				font-size: 24rpx;

				&:before {
					content: '';
					display: block;
					position: absolute;
					top: 0;
					left: -12rpx;
					height: 80%;
					width: 2rpx;
					background: rgba(255, 255, 255, 0.05);
				}

				text {
					font-size: 34rpx;
					color: #e6ab3d;
					padding-right: 10rpx;
				}
			}

			.sign {
				flex: 60rpx;
				flex-grow: 0;
				text-align: right;
				text-align: center;

				.iconfont {
					font-size: 26px;

				}

				.scoreIdx {
					line-height: 50rpx;
					color: rgba(255, 255, 255, .5);
					position: relative;
					color: #767676;

					&.scoreIdx-1 {
						color: #ffbf00;
					}

					&.scoreIdx-2 {
						color: #dfdfdf;
					}

					&.scoreIdx-3 {
						color: #934010
					}

					.idx {
						position: absolute;
						left: 50%;
						top: 50%;
						transform: translate(-50%, -50%);
						z-index: 2;
						color: #fff;
						font-size: 14px;
						text-shadow: 0 0 2px #666;
					}
				}
			}
		}

		.match-state {
			position: absolute;
			top: 120rpx;
			right: 120rpx;
			font-size: 64rpx;
			color: #999;

			.iconfont {
				font-size: 140%;
				margin-right: 20rpx;
				color: #999;
				display: block;
			}

			.txt {
				font-size: 20rpx;
				text-align: center;
				margin-right: 20rpx;
			}

			&.active {
				color: #ff9900;

				.iconfont {
					color: #ff9900;
				}
			}
		}
	}
</style>