<template>
	

	<view class="card item" :class="isHistory?'isHistory' :''">
			<view class="header">
				<view class="my-title">
					{{(""+item.beginDate).substring(0,16)}}
				</view>
				<view class="nowrap" style="width:160upx;">
					{{item.city}}
				</view>
				<view class="my-tag" >
					{{$filters.tryEnum('partyType',item.type)}}
				</view>
			</view>
			<view style="line-height: 1.8;">
				<view class="flex flex-between">
					<view v-if="item.name" class="nowrap" style="flex: 60%;">
						{{item.name}}
					</view>
					<view class="f-color-warn f-sm">
						{{item.distance}}
					</view>
				</view>
				<view class="flex flex-justify mt address">
					<view v-if="item.name" class="address-txt" style="flex: 60%;">
						{{item.address}}
					</view>
					<view class="f-color-light f-sm">	
						热度:{{item.static.hotCount}}
					</view>
				</view>
			</view>
			
			<view class="flex flex-between flex-center">
				<view class="flex flex-center" style="gap: 5px;">
					<my-user-avatar size="mini" :data="item.user.avatar"></my-user-avatar>
					<view class="">
						{{item.user.nickName}}
					</view>
				</view>
				<view style="text-align: right;flex:60%">
					<view class="my-tag light" >
						{{$filters.tryEnum('partySize',item.size)}}
					</view>
					<view class="my-tag warn" >
						{{$filters.tryEnum('venueType',item.venueType)}}
					</view>

					<view class="my-tag danger-light" >
						{{$filters.tryEnum('partyPaid',item.paid)}}
					</view>
				</view>
			</view>

	</view>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { appStore } from '../store';
import { userStore } from '../store/user';

	const now = new Date();
	const props = defineProps(["item","isHistory"]);
	const app = appStore();
	const isHistory = computed(()=>{
		return now > new Date(props.item.beginDate)
	})

</script>

<style lang="scss">
	.expireCls {

		filter: grayscale(.5);
		opacity: .8;
	}

	.item{
		padding: 10px;
		//margin: 20rpx 20rpx;
		margin-bottom: 0;
		// box-shadow: 8rpx 8rpx 0 rgba(0,0,0,.3);
		// border: 1px solid rgba(255, 255, 255, 0.1);
		position: relative;
		&.isHistory{
	
			filter: grayscale(1);
			opacity: 1;
		}
		.header{
			display: flex;
			justify-content: space-between;
			align-items: center;
		}
		.headerImg{
			width: 100%;
			height: 240rpx;
			//border: 2px solid rgba(255, 255, 255, 0.45098039215686275);
			outline: 4px solid rgba(0, 0, 0, 0.090);
			&-null{
				background: rgba(56, 35, 95, 0.68);
				text-align: center;
				line-height: 240rpx;
				color:rgba(255, 255, 255, 0.3) ;
			}
		}
		.my-tag{
			font-size: $uni-font-size-sm;
			padding: 0 10rpx;
			line-height: 1.7;
		}
		.paid-tag{

			color: #fff;
			display: inline-block;
			&.paid-tag-1{
				background: rgba($uni-color-success,.5)
			}
			&.paid-tag-2{
				
				background: rgba($uni-color-error,.5)
			}
		}
		
		.address{
			align-items: flex-start;
			.address-icon{
				flex:0;
				width: 32upx;
				height: 32upx;
				font-size: 28upx;
				line-height:32upx;
				opacity: .7;
			}
			.address-txt{
				flex-grow: 1;
				color: rgba(255,255,255,.5);
				font-size: $uni-font-size-sm;
			}

		}
		
	}

</style>
