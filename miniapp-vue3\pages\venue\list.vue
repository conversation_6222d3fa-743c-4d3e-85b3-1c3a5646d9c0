<template>

		<z-paging
		ref="listRef" 
		v-model="d.list"
		@query="load" >
		<template #top>
			<my-page-header slot="top" title="场地" :showBack="true"></my-page-header>
			<navigator v-if="user.isAdmin || app.sys.allowPushVenueByUser==1" url="edit"><my-fab ></my-fab></navigator>
		</template>
			<view class="page-container">
				<navigator class="item-box" :url="`/pages/venue/view?id=${item.id}`"
					v-for="(item,index) in d.list" :key="index">
					<my-item-venue :item="item" expireCls='expireCls'></my-item-venue>
				</navigator>
			</view>

		</z-paging>

</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import{ onShareTimeline,onShareAppMessage,onLoad} from "@dcloudio/uni-app"
import { userStore } from "../../store/user";
import { appStore } from "../../store";
import ajax from "../../common/ajax";
import { getDistance } from "../../common/funs";
const app = appStore();
const listRef = ref();
const user = userStore();
	const d = reactive({
		cityPickerValueDefault: [0, 0, 1],
		searchParams:{
			where:{
				divisionCode:0
			}
		},
		list:[] as IVenue[]
	})

		onShareTimeline(()=>{
			return {
				title:`我的毽球活动场地`,
				query: `where=${encodeURIComponent(JSON.stringify(d.searchParams.where))}`,
			}
		})
		onShareAppMessage(()=>{
			return {
			  title: `我的毽球活动场地`,
			  path: `/pages/venue/list?where=${encodeURIComponent(JSON.stringify(d.searchParams.where))}`,
			}
		})
		onLoad(async ({where}:any)=> {

			wx.showShareMenu({
			  withShareTicket: true,
			  menus: ['shareAppMessage', 'shareTimeline']
			})
			
			if(where){
				let _w = JSON.parse(decodeURIComponent(where))
				d.searchParams = {
					where:_w,
				}
			}else{
				d.searchParams = {
					where:{
						divisionCode:user.tryLocal.divisionCode
					}
				}
			}
		})
		

		function load(pageIndex:number=1,pageSize:number=15){
			ajax.get<any>('/venue',{
					pageIndex,pageSize,
					divisionCode:user.tryLocal.divisionCode
				}).then(res=>{
					let data = res.list.map((v:any)=>parseItem(v));
					//console.log(data)
					listRef.value.complete(data)
					
				})
				.catch((err : any) => {
					listRef.value.complete(false);
				})
			}
			

			function parseItem(item:any){
				
				if(user.currentLocal.lat){
					item.distance =  getDistance(user.currentLocal.lat,user.currentLocal.lon,item.lat,item.lon).toFixed(1)+'公里';
				}
				item.images = (item.images||"").split(',')
				return item;
			}

</script>

<style lang="scss">

</style>
