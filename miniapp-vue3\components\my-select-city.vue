<template>
	<view>
		<picker mode="multiSelector" @change="pickerChange" @columnchange="columnChange" :value="d.selectIdxs"
			:range="app.divisonCityMap">
			<view class="picker">
				<text :class="'f-color-light f-sm'">{{label||''}}</text>
				<text :class="'f-color-warn f-sm'"> {{
					app.divisonCityMap[d.selectIdxs[0]].name
					|| '-'}} ● {{
						app.divisonCityMap[d.selectIdxs[0]].citys[d.selectIdxs[1]].name  || '-'
					}}</text>
			</view>
		</picker>
	</view>
</template>

<script lang="ts" setup>
	import { computed, reactive, ref, watch } from 'vue';
	import { userStore } from '../store/user';
	import { appStore } from '../store';
	const user = userStore();
	const app = appStore();
	const props = defineProps(['allProvince', 'allCity', 'label','modelValue']);

	const emit = defineEmits(['update:modelValue']);
	const divisionCityMap = app.divisonCityMap;
	const d = reactive({
		divisionCode: user.tryLocal.divisionCode, 
		selectIdxs: [0, 0],
		provinceList: divisionCityMap.map(v => v.name),
		cityList: [] as string[],
		province: "",
		city: ""
	})
	watch(() => d.selectIdxs[0], () => {
		d.province = divisionCityMap[d.selectIdxs[0]].name

		d.city = '';
		d.divisionCode = 0;
		d.cityList = divisionCityMap[d.selectIdxs[0]].citys.map(v => v.name)
	}, { deep: true })
	watch(() => d.selectIdxs[1], () => {
		let selectedNode = divisionCityMap[d.selectIdxs[0]].citys[d.selectIdxs[1]];
		d.city = selectedNode.name;
		d.divisionCode = selectedNode.code;
	}, { deep: true })
	
	watch(
		() => props.modelValue,
		(newValue) => {
			let selectedIdxs = [0,0]
			app.divisonCityMap.every((p,pIdx)=>{
				let cIdx = p.citys.findIndex((c=>c.code==newValue));
				if(cIdx!=-1){
					selectedIdxs[0] = pIdx
					selectedIdxs[1] = cIdx
					return false;
				}else{
					return true;
				}
			})
			d.selectIdxs = selectedIdxs;
			
		}
	);


	function pickerChange(e : any) {
		d.selectIdxs[0] = e.detail.value[0];
		d.selectIdxs[1] = e.detail.value[1];
	}
	function columnChange(e : any) {

	}
</script>

<style lang="scss">

</style>