<template>
	<view>
		<picker :class="{readonly:props.readonly!=undefined}" mode="multiSelector" @change="pickerChange" @columnchange="columnChange" :value="d.selectIdxs"
			:range="app.divisonCityMap">
			<view class="picker">
				<text :class="'f-color-light f-sm'">{{label||''}}</text>
				<text :class="'f-color-warn f-sm'"> {{
					app.divisonCityMap[d.selectIdxs[0]].name
					|| '-'}} ● {{
						app.divisonCityMap[d.selectIdxs[0]].citys[d.selectIdxs[1]].name  || '-'
					}}</text>
			</view>
		</picker>
	</view>
</template>

<script lang="ts" setup>
	import { computed, reactive, ref, watch } from 'vue';
	import { userStore } from '../store/user';
	import { appStore } from '../store';
	const user = userStore();
	const app = appStore();
	const props = defineProps(['allProvince', 'allCity', 'label','modelValue','readonly']);

	const emit = defineEmits(['update:modelValue']);
	const divisionCityMap = app.divisonCityMap;
	const d = reactive({
		divisionCode: user.tryLocal.divisionCode.toString(), 
		selectIdxs: [0, 0],
		provinceList: divisionCityMap.map(v => v.name),
		cityList: [] as string[],
		province: "",
		city: ""
	})
	watch(() => d.selectIdxs[0], () => {
		d.province = divisionCityMap[d.selectIdxs[0]].name
		d.city = '';
		d.divisionCode = '';
		d.cityList = divisionCityMap[d.selectIdxs[0]].citys.map(v => v.name)
	}, { deep: true })
	watch(() => d.selectIdxs[1], () => {
		let selectedNode = divisionCityMap[d.selectIdxs[0]].citys[d.selectIdxs[1]];
		d.city = selectedNode.name;
		d.divisionCode = selectedNode.code.toString();
	}, { deep: true })
	
	watch(
		() => props.modelValue,
		(newValue) => {
			let selectedIdxs = [0,0];
			let cityDivisionCode = getCityDivisionCode(newValue);
			app.divisonCityMap.find((p,pIdx)=>{
				let cIdx = p.citys.findIndex((c=>c.code==cityDivisionCode));
				console.log(p.name)
				if(cIdx!=-1){
					selectedIdxs[0] = pIdx
					selectedIdxs[1] = cIdx
					return true;
				}else{
					return false;
				}
			})
			d.selectIdxs = selectedIdxs;
			
		}
	);


	function pickerChange(e : any) {
		d.selectIdxs[0] = e.detail.value[0];
		d.selectIdxs[1] = e.detail.value[1];
	}

	function getCityDivisionCode(code:string|number){
		return code.toString().substring(0,4)+"00"
	}
	function columnChange(e:any) {
		switch (e.detail.column) {
			case 0:
				// const _citys = this.source[e.detail.value].children
				// this.data.splice(1,1,_citys)
				
				// this.idxs[0] = e.detail.value;
				// this.idxs[1] = 0
	
		}
	}
	
</script>

<style scoped lang="scss">
.readonly{
	pointer-events: none;
}
</style>