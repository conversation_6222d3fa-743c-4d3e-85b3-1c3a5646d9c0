<template >
	<view class="my-cell" :class="(size?size:'')+(line?' line-'+line+' ':' ')+(cls?cls:'')+(disabled===undefined || disabled==false?'':'disabled') + ((url)?' hover':'')" @click="go">

		<view class="left">
			<view class="leftIcon" v-if="$slots.iconSlot || icon">
				<slot name="iconSlot"></slot>
				<view v-if="icon" :class="icon" class="icon"></view>
			</view>
		</view>
		
		<view class="label" :style="{width:labelWidth?labelWidth+'upx':'auto'}" >
			<view class="labelTxt" :class="labelCls">
				{{label}}
			</view>
			
			<view v-if="labelExt!==undefined" class="labelExt" >
				{{labelExt}}
			</view>
		</view>
			
		<view class="middle">
			<slot></slot>
		</view>
			
		<template v-if="$slots.rightSlot || right!=null || rightIcon">
			<view class="right" :class="rightCls" @tap="_rightClick">
				<template v-if="right!==undefined">
					{{right}}
				</template>
				<template v-if="$slots.rightSlot">
					<slot name="rightSlot" class="right"></slot>
				</template>
			</view>
		</template>
			
		<view v-if="rightIcon" @tap="_rightIconClick" class="icon rightIcon" :class="rightIcon"></view>
		<view v-if="arrow!==undefined" @tap="_rightIconClick" class="iconfont icon-right-arrow"></view>
	</view>
</template>

<script lang="ts">
	export default {
		name:"my-cell",
		data() {
			return {
				
			};
		},
		mounted() {
		},
		props:['icon','label','labelCls','labelExt','labelWidth','right',"rightIcon",'url','size','line','cls','rightCls',"arrow",'disabled'],
		methods:{
			go(e:Event){
				this.$emit('fullClick')
				// if(this.url){
				// 	uni.navigateTo({url:this.url})
				// }

				
			},
			_rightClick(e:Event){
					this.$emit('rightClick')
					//this.$emit('fullClick')
					//e.stopPropagation()

			},
			_rightIconClick(e:Event){
					this.$emit('rightIconClick')
					//this.$emit('fullClick')
					//e.stopPropagation()

			}
		}
	}
</script>

<style lang="scss" >
	// my-cell{
	// 	display: block;
	// 	&+&{
	// 		border-top: 1upx solid $color-gray;
	// 	}
	// }
	

.my-cell{
	display: flex;
	justify-content:space-between;
	align-items: center;
	// line-height: 3.2;
	height: 80upx;
	// margin: 0 -20rpx;
	// padding: 0 20rpx;
	//margin: auto 30upx;
	// padding-left: 10px;
	// padding-right: 10px;
	input{
		border: 0;
		text-align: end;
	}
	&.hover{
		&:active{
			// background: $color-gray;
			.label,.icon-arrow,.right{
				color:$uni-text-color-grey;
			}
			.icon-arrow{
				margin-right: -10rpx;
			}

		}
	}
	&.disabled view{
		opacity: .4;
		filter: grayscale(50%);
		pointer-events: none;
	}
	.icon{
		flex-grow: 0;
	}
	&.lg{
		font-size:$uni-font-size-lg;
	}
	&.hx1{
			height: 90upx;
	}
	&.hx2{
			height: 100upx;
	}
	&.sm{
		height: 56upx;
	}
	&.space{
		height: 96upx;
		padding-top: 10upx;
		padding-bottom: 10upx;
	}
	&.mini{
		line-height: 32upx;
		font-size:$uni-font-size-sm;
	}
	&.auto{
		height:initial;
		min-height: initial;
	}
	
	// &:last-of-type{
	// 	border-bottom: 0upx;
	// }
	//for h5
	
	&+&{
		//margin-top: 2upx;
		border-top: 2upx solid $uni-bg-color;
	}
	.left{
		padding-right: 10upx;
		// color: $font-color-dark;
		flex-shrink: 0;
		display: flex;
	}
	.leftIcon{
		flex-shrink: 0;
		margin-right: 10upx;
	}
	.label{
		white-space:nowrap;
		overflow:hidden; 
		text-overflow:ellipsis;
		min-width: 60px;
		
	}
	.labelTxt{
		line-height: 1.6;
		white-space:nowrap;
		overflow:hidden; 
		text-overflow:ellipsis; 
	}
	.labelExt{
		// margin-top: 10upx;
		color: $uni-text-color-grey;
		font-size: .9em;
		line-height: 1.6;
		&.sm{
			margin-top: 0upx;
			font-size: .75em;
		}
		&.mini{
			
		}
		&.lock{
			height:30upx
		}
	}
	&.gray{
		.label{
			color:$uni-text-color-grey;
		}
	}
	.middle{
		flex-grow: 100;
		flex-shrink: 100;
		margin-right: 20upx;
	}
	.right{
		flex-grow: 100;
		text-align: right;
		color:#fff;
		&-icon{
			flex-shrink: 0;
			margin-right: -8upx;
			text-align: right;
			height: 86upx;
		}
	}
	.icon-arrow{
		color:$uni-text-color;
		transition: all .2s ;
		margin-right: -5px;
	}
	//styleCls 模式样式
	&.input{
		.label{
			color: $uni-text-color-grey;
		}
		input{
			color: $uni-text-color-grey;
			
		}
		&.readOnly{
			.right,.rightIcon{
				color: $uni-text-color-grey;
			}
		}
	}
	
}
</style>
