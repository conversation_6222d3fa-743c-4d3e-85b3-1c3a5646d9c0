import App from './App'
import * as <PERSON><PERSON> from 'pinia';
import { createSSRApp } from 'vue'
import { appStore } from './store'
export function createApp() {
	
  const app = createSSRApp(App)
  const pinia = Pinia.createPinia()
  app.use(pinia)
  
  
  const app_stroe = appStore();
  app.config.globalProperties.$filters = {
	  tryEnum:app_stroe.tryEnum
  }
  

  app_stroe.init();
  
  return {
    app
  }
}
