import { getCurrentInstance } from "vue";
import { appStore } from "../store";
import ajax from "./ajax";

//计算坐标两点直线距离 return的距离单位为km
export function getDistance(lat1 : any, lng1 : any, lat2 : any, lng2 : any) {
	console.log('getDistance')
	var radLat1 = lat1 * Math.PI / 180.0;
	var radLat2 = lat2 * Math.PI / 180.0;
	var a = radLat1 - radLat2;
	var b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
	var s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) +
		Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
	s = s * 6378.137;// EARTH_RADIUS;
	s = Math.round(s * 10000) / 10000;
	return s;
}


export const weeks = [
	"周日",
	"周一",
	"周二",
	"周三",
	"周四",
	"周五",
	"周六",
];

export function formatDate(date : any, fmt = "yyyy-MM-dd") {

	if (!date) { return "" }
	date = new Date(new Date(date).toUTCString());
	if (/(y+)/.test(fmt)) {
		fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substring(4 - RegExp.$1.length));
	}
	let o = {
		'M+': date.getMonth() + 1,
		'd+': date.getDate(),
		'h+': date.getHours(),
		'm+': date.getMinutes(),
		's+': date.getSeconds(),
		'w+': weeks[date.getDay()],
	} as any;
	for (let k in o) {
		if (new RegExp(`(${k})`).test(fmt)) {
			let str = o[k] + '';
			fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? str : (('00' + str).substring(str.length)));
		}
	}
	return fmt;
};
//图床图片优化显示
export const imgUtil = {
	SIZE: {
		min: '?imageView2/1/w/100/h/100',
		sm: '?imageView2/1/w/160/h/160',
		base: '?imageView2/1/w/220/h/220',
		big: '?imageView2/1/w/320/h/320',
		lg: '?imageView2/1/w/600/h/600',
	} as Record<string, string>,

	//图片视频自动识别
	reSize(url : string, size : string = 'sm') : any {
		if (!url) {
			return '';
		}
		if (url.indexOf('file://') != -1) {
			return url;
		}

		if (imgUtil.isVideo(url)) {
			return url + '?x-oss-process=video/snapshot,t_7000,m_fast'
		} else {
			return url + (imgUtil.SIZE[size])
		}
	},
	isVideo(url : any) {
		return url.indexOf('video_') != -1
	},
	//压缩 return url canvsSize需要响应对象将重写canvsize画布宽高
	async compressImage(vm : any, canvasId : string, path : string, limitSize = { w: 750, h: 500 }, canvasSize = { w: 750, h: 500 }, compress = .7) : Promise<string> {

		const ins = getCurrentInstance();

		return new Promise(async (r, j) => {
			//获取图片信息
			let res = await wx.getImageInfo({ src: path })

			var ctx = uni.createCanvasContext(canvasId, ins?.proxy); // 创建画布 :目前开发工具版本，此处会报toJson错误，担不影响功能

			if (limitSize) {
				if (res.height > res.width) { //长图
					canvasSize.h = limitSize.h;
					canvasSize.w = Math.trunc(canvasSize.h * res.width / res.height)
				}
				if (res.height <= res.width) {
					canvasSize.w = limitSize.w;
					canvasSize.h = Math.trunc(canvasSize.w * res.height / res.width) //根据图片比例换算出图片高度
				}

			} else {
				limitSize = { w: res.width, h: res.height }
				canvasSize.w = res.width;
				canvasSize.h = res.height;
			}
			console.log('宽高----------')
			console.log([limitSize.w, limitSize.h])

			ctx.drawImage(path, 0, 0, res.width, res.height, 0, 0, canvasSize.w, canvasSize.h);
			// ctx.setFontSize(28)
			// ctx.setFillStyle('gray') 
			// ctx.fillText("OO", 0, 0)

			setTimeout(() => {
				ctx.draw(false, () => {
					//!!!此处必须指定,x,y,width,height,desWidth,destHeight,否则真机上图切不完整
					wx.canvasToTempFilePath({
						canvasId: canvasId,
						fileType: "jpg",
						x: 0,
						y: 0,
						width: canvasSize.w,
						height: canvasSize.h,
						destWidth: canvasSize.w,
						destHeight: canvasSize.h,
						quality: compress,
						success: async (temRes) => {
							//console.log([path,temRes.tempFilePath])
							//console.log(wx.getFileSystemManager().readFileSync(temRes.tempFilePath))
							//console.log(await wx.getImageInfo({src: temRes.tempFilePath}))

							r(temRes.tempFilePath);
						},
						fail(e) {
							j(e)
						}
					})

				},ins?.proxy)
			}, 500)

		})
	}
}


//获取用户当前位置，若失败将使用本地缓存中的位置lon,lat,city,p...
export async function getCurrentLoc() : Promise<ILocation> {
	return new Promise(async (r, j) => {
		try {
			let res = await uni.getLocation()
			console.log(res)
			if (res.errMsg != 'getLocation:ok') {
				uni.showModal({
					title: '未能获取您的位置',
					content: '有些功能将无法提供，若需开启请修改权限',
					showCancel: false,
					success() {
						uni.openSetting({
							success(res) {
								console.log(res.authSetting)
							}
						})
						j()
					}
				})

			} else {
				let obj = await getLocByLL(res.latitude, res.longitude)

				r({
					lat: res.latitude,
					lon: res.longitude,
					province: obj.province,
					city: obj.city,
					area: obj.area,
					divisionCode: obj.divisionCode,
					country: ''
				})
			}
		} catch (ee) {
			debugger
		}
	})
}
//通地腾讯地图api 根据坐标，返回省市区
export async function getLocByLL(lat : any, lon : any) {
	let res = await uni.request({ url: 'https://apis.map.qq.com/ws/geocoder/v1/?location=' + lat + ',' + lon + "&key=OC7BZ-GZVRJ-ADJFV-KVJKB-2AAGK-BDFLM" }) as { errMsg : string, data : any }
	if (res.errMsg != "request:ok") {
		throw new Error(res.errMsg);
	}

	let province = res.data.result.address_component.province.replace(/省$|市$|盟$|/, '')
	let city = res.data.result.address_component.city.replace(/市$/, '')
	let area = res.data.result.address_component.district;
	let adcode = res.data.result.ad_info.adcode
	return {
		contry: res.data.result.contry,
		province,
		city,
		area,
		divisionCode: adcode
	}
}

//wx文本、图片安全识别，通过或网络报错返回true,未通过检验抛异常
export async function wxContentCheck(content : string, type = 'txt') {
	const app = appStore();
	if (!app.sys.msgCheck) {
		return Promise.resolve(true);
	}

	let res : any = true;
	try {
		if (type == 'img') {
			res = await ajax.upload('/contentCheck?type=' + type, content);
		} else {
			res = await ajax.post('/contentCheck?type=' + type, { content });
		}

	} catch (e) {
		//如果异常，输出check true;
		return res;
	}
	if (res == false) {
		uni.showToast({
			icon: "none",
			title: "安全检查：内容或图片可能违规"
		});
		throw new Error('内容安全检查：内容可能有违规')
	}

}
// 解析二进制枚举值
// 例如：_enum = {1: "小花", 2: "大白", 4: "网毽", 8: "比赛"}
// vals = 3 (二进制为 11)，则返回 [{ name: "小花", value: 1 }, { name: "大白", value: 2 }]
// 注意：_enum 的 key 必须是 2 的幂次方，如 1, 2, 4, 8 等
export function parseBinaryEnums(_enum : Record<number, string>, vals : number) {
	if (_enum == null) return [];
	const result : { name : string; value : number }[] = [];
	for (const key in _enum) {
		const value = Number(key);
		/*
		(vals & value) === value 在你的场景下也是对的，因为你的 value 都是 1、2、4、8 这种单一位（只有一个二进制位为 1），所以 (vals & value) === value 和 (vals & value) !== 0 效果一样。
		区别：
		如果 value 可能是多个位的组合（比如 3 = 0b11），那 (vals & value) === value 可以判断 vals 是否包含所有这些位。
		但你现在 value 都是单一位，所以两种写法都可以。
		*/
		if ((vals & value) === value) { // 检查是否包含该值
			result.push({ name: _enum[value], value });
		}
	}
	return result;
}