<template>
	<view class="page-imgbg" :style="{'background-image':$asset('bg','bg')}" style="height:100vh;overflow: hidden; padding-top: 260rpx;">
		<view class="sheader" :style="{'background-image':$asset('my_head_bg','bg')}">
			<view class="row-flex-auto" style="position: absolute;left: 220rpx;top: 120rpx;">
				<myAvatar :d="d.user"></myAvatar>
				<view style="margin-left: 20rpx;" class="f-big ">
					<view class="f-big nowrap" style="color:#fff;width: 300rpx;">{{ d.user.nickName }}</view>
					<view class="f-color-light f-sm nowrap" style="width: 300rpx;">
						{{ d.user.sign || "" }}
					</view>
				</view>
			</view>
			<view @click="goBack" class="iconfont iconarrowRight nav-backBtn"></view>
		</view>
		<view style="padding: 30rpx 40rpx;">
			<view class="row-flex-around">
				<view class="btn-box row-flex-justify" :class="{'active':d.currentTab=='post'}" v-if="base.sys.allowUserPost==1" @tap="d.currentTab='post'">
					<view class="iconfont iconedit"></view>
					<view class="">动态</view>
				</view>
				<view class="middle-line" v-if="base.sys.allowUserPost==1"></view>
				<view class="btn-box row-flex-justify" :class="{'active':d.currentTab=='visit'}" @tap="d.currentTab='visit'">
					<view class="iconfont iconicon_list_daohang"></view>
					<view class="">签到</view>
				</view>
				<view class="middle-line" v-if="base.sys.allowUserGallery==1"></view>
				<view class="btn-box row-flex-justify" :class="{'active':d.currentTab=='gallery'}" v-if="base.sys.allowUserGallery==1" @tap="d.currentTab='gallery'">
					<view class="iconfont iconxiangce1"></view>
					<view class="">相册</view>
				</view>
			</view>
		</view>
		<view class="container">
			<scroll-view scroll-y style="height:calc(100vh - 430rpx)">
				<swiper style="height: 100%;" :indicator-dots="false" @change="e=>d.currentTabSw=e.detail.current" :autoplay="false" :duration="700" :current="d.currentTabSw">
					<swiper-item v-if="base.sys.allowUserPost==1">
						<view class="swiper-item">
							<view class="my-title f-color-light">最近动态</view>
							<view class="my-box dark"  style="position: relative;min-height: 100rpx;">
								<view :class="{'line-1':idx<(d.postTopData.length-1)}" v-for="item,idx in d.postTopData" :d="item" :key="idx">
									<postItem :d="item"></postItem>
								</view>
							</view>
						</view>
					</swiper-item>
					<swiper-item>
						<view class="swiper-item">
							<view class="my-title f-color-light">最近签到</view>
							<view class="my-box dark" style="position: relative;min-height: 100rpx;">
								<view :class="{'line-1':idx<(d.visitTopData.length-1)}" v-for="item,idx in d.visitTopData" :d="item" :key="idx">
									<view class="visit-item">
										<view class="item-date">
											<text class="date-month">{{item.month}}月</text>
											<view class="date-day">{{item.day}}</view>
										</view>
										<view class="item-content">
											<text class="my-tag light">{{item.time}}分</text>
											<view class="" style="padding: 10rpx;">
												拜访了 <text class=" f-strong"># {{item.venueName || ""}} #</text>
											</view>
										</view>
									</view>
								</view>
							</view>
						</view>
					</swiper-item>
					<swiper-item v-if="base.sys.allowUserGallery==1">
						<view class="swiper-item">
							<view class="my-box dark"  style="position: relative;min-height: 100rpx;">
								<view class="gallery-item" v-for="item,idx in d.galleryData" :d="item" :key="idx">
									<image class="img" mode="aspectFill" lazy-load :src="item + '?imageView2/1/w/120/h/120'" @tap="viewImage(d.galleryData, idx)"></image>
								</view>
							</view>
						</view>
					</swiper-item>
				</swiper>
			</scroll-view>
		</view>
		<loginDoor></loginDoor>
	</view>
</template>

<script lang="ts" setup>
import { reactive, computed, watch } from 'vue'
import { onLoad, onShareAppMessage, onShareTimeline } from '@dcloudio/uni-app'
import { appStore } from '../../store'
import { userStore } from '../../store/user'
import ajax from '../../common/ajax'
import { formatDate } from '../../common/funs'
import postItem from "@/components/logic/post-item.vue"

const app = appStore()
const userStoreInstance = userStore()
const base = computed(() => app)
const me = computed(() => userStoreInstance.info)

const d = reactive({
	id: '',
	user: {} as any,
	currentTab: 'visit',
	tabbars: [] as string[],
	postTopData: [] as any[],
	visitTopData: [] as any[],
	galleryData: [] as any[],
})

const currentTabSw = computed({
	get() {
		return d.currentTab ? d.tabbars.indexOf(d.currentTab) : 0
	},
	set(idx: number) {
		d.currentTab = d.tabbars[idx]
	}
})

watch(base, () => {
	initTabbars()
})

onLoad(({ id }: any) => {
	d.id = id
	wx.showShareMenu({
		withShareTicket: true,
		menus: ['shareAppMessage', 'shareTimeline']
	})
	initTabbars()
	load()
})

onShareTimeline(() => {
	return {
		title: `${d.user.nickName}的毽球空间`,
		query: `id=${d.user.id}`,
	}
})

onShareAppMessage(() => {
	return {
		title: `${d.user.nickName}的毽球空间`,
		path: '/pages/View?id=' + d.user.id
	}
})

function initTabbars() {
	d.tabbars = []
	if (base.value.sys.allowUserPost == 1) d.tabbars.push('post')
	d.tabbars.push('visit')
	if (base.value.sys.allowUserGallery == 1) d.tabbars.push('gallery')
}

function load() {
	if (me.value.token) {
		loadUserInfo()
		loadPostTop()
		loadVisitTop()
		// loadGallery()
	}
}

async function loadUserInfo() {
	d.user = await ajax.get(`/user/${d.id}/info`)
	uni.setNavigationBarTitle({
		title: d.user.nickName + '的空间'
	})
}

async function loadPostTop() {
	const res = await ajax.get(`/user/${d.id}/posts`, { pageSize: 5 })
	d.postTopData = res.list
}

async function loadVisitTop() {
	const res = await ajax.get(`/user/${d.id}/visits`, { pageSize: 5 })
	res.list.forEach((v: any) => {
		if (!v.month) {
			const s = formatDate(v.updatedAt, 'MM,dd,hh:mm').split(",")
			v.month = s[0]
			v.day = s[1]
			v.time = s[2]
		}
	})
	d.visitTopData = res.list
}

// 目前接口报错，暂不启用
// async function loadGallery() {
// 	const res = await ajax.get(`/user/${d.id}/gallery`)
// 	d.galleryData = res.list.map((v: any) => v.url)
// }

function viewImage(urls: string[], idx = 0) {
	wx.previewImage({
		urls,
		current: urls[idx]
	})
}

function goBack() {
	uni.navigateBack()
}
</script>

<style lang="scss" scoped>
	.middle-line {
		width: 2rpx;
		background: rgba(255, 255, 255, 0.2);
		height: 50rpx;
	}

	.btn-box {
		border-radius: 2rpx;
		color: rgba(255, 255, 255, .7);
		border: 0;
		// box-shadow: 8rpx 8rpx 0 rgba(0,0,0,.3);
		border: 1rpx solid rgba(255, 255, 255, 0.0);
		background: transparent;
		background: linear-gradient(0deg, rgba(20, 9, 142, 0.0) 0, rgba(86, 1, 114, 0.0) 100%);
		padding: 5rpx 30rpx 5rpx 10rpx;
		transition: all .3s;


		&:active {
			background: rgba($color: white, $alpha: .3);
			color: rgba(255, 255, 255, 1);
			transform: scale(1.1);
		}

		&.active {
			background: rgba($color: white, $alpha: .2);
		}

		.iconfont {
			font-size: 2.8em;
			margin-right: 10rpx;
		}
	}

	.sheader {
		position: fixed;
		z-index: 12;
		top: 0;
		height: 260rpx;
		width: 100vw;
		background-repeat: no-repeat;
		background-size: cover;
		background-position: bottom center;
	}


	.nav-backBtn {
		position: absolute;
		top: 50rpx;
		left: 10rpx;
		padding: 6rpx 0rpx 6rpx 0;
		margin-top: 20rpx;
		display: inline-block;
		transform: rotate(180deg);
		background: transparent;
		text-align: center;
		color: #fff;
		vertical-align: middle;
		font-size: 60rpx;

		&:active {
			color: rgb(124, 181, 204);
		}
	}

	.gallery-item {
		display: inline-block;
		position: relative;
		padding: 10px;

		.img {
			width: 120rpx;
			height: 120rpx;
			margin: 0rpx;
			border: 1rpx solid rgba($color: #fff, $alpha: 1);
			border-radius: 4rpx;
			display: inline-block;
			box-shadow: 0rpx 0rpx 8rpx rgba($color: #853a05, $alpha: .1);
		}
	}

	.visit-item {

		display: flex;
		align-items: flex-start;
		margin: 20rpx;

		.item-time {
			font-size: $uni-font-size-sm;
			padding-left: 20rpx;
			opacity: .5;
			width: 80rpx;
		}

		.item-date {
			color: #FFFFFF;
		}

		.item-content {
			flex: 100%;
			margin-left: 10rpx;
			padding: 0rpx 10rpx 10rpx 10rpx;
			//background: rgba($color: #fff, $alpha: .1);
		}

		.date-month {
			font-size: .8em;
		}

		.date-day {
			font-size: 1.5em;
			font-weight: bold;
		}

		.post-img {
			width: 100rpx;
			height: 100rpx;
			margin: 10rpx 10rpx 0rpx 0;
			border-radius: 4rpx;
		}
	}
</style>
