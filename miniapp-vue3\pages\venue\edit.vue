<template>

	<view class="page-bottom-fix">
		<my-page-header :showBack="true" :title="d.id?'场地编辑':'新增场地'"></my-page-header>
		<view class="container">
			<my-cell label="场地名称" line="2">
				<template #rightSlot>
					<input  placeholder="场所地点的名称🖋" type="text" v-model="d.model.name" />
				</template>
			</my-cell>
			<my-cell label="所在省市" line="2">
				<template #rightSlot>
				<view class="f-color-dark" >
					{{d.model.province}} {{d.model.city}}
				</view>
				</template>
			</my-cell>
			<map class="map" id="map" :longitude="d.model.lon" :latitude="d.model.lat"
				:markers="[{id:1,width:20,height:30,latitude:d.model.lat,longitude:d.model.lon,name:d.model.name}]"
				scale="15" style="width: 100%; height: 160px;" @tap="chooseLocation"></map>
				
			<my-cell label="地图位置" @rightClick="chooseLocation" right="点击选择" arrow=1 line="1"></my-cell>



			<my-cell label="详细地址" line="2">
				<template #rightSlot>
					<input style="width: 260px;font-size: .9em;text-align: right;" placeholder="点击输入🖋" type="text" v-model="d.model.address" />
				</template>
				
			</my-cell>


			<my-cell label="推荐指数" line="2">
				<template #rightSlot>
				<view class="flex" style="justify-content: flex-end;">
					<my-rate v-model="d.model.star" :max="5" color="#fff" active-color="#ff9d00" :size="20" />
				</view>
				</template>
			</my-cell>
			
			<my-cell label="场地介绍"></my-cell>
			<textarea v-model="d.model.intro" class="my-textarea "
				style="height: 80px;"></textarea>
				
			<my-cell label="规模" line="2">
				<template #rightSlot>
				<my-switch size="sm" :source="enums.venueSize" v-model="d.model.size"></my-switch>
				</template>
			</my-cell>

			<my-cell label="场地" line="2">
				<template #rightSlot>
				<my-switch size="sm" :source="enums.venueType" v-model="d.model.type"></my-switch>
				</template>
			</my-cell>
			<my-cell label="停车位" line="2">
				<template #rightSlot>
				<my-switch size="sm" :source="enums.venueParking" v-model="d.model.parking"></my-switch>
				</template>
			</my-cell>
			<my-cell label="使用情况" line="2">
				<template #rightSlot>
				<my-switch size="sm" :source="enums.venueAble" v-model="d.model.able"></my-switch>
				</template>
			</my-cell>
			<my-cell label="是否收费" line="2">
				<template #rightSlot>
				<my-switch size="sm" :source="enums.venuePaid" v-model="d.model.paid"></my-switch>
				</template>
			</my-cell>
			<my-cell label="联系人" line="2">
				<template #rightSlot>
				<input placeholder="联系人姓名🖋" type="text" v-model="d.model.contacter" />
				</template>
			</my-cell>
			<my-cell label="联系电话" line="2">
				<template #rightSlot>
				<input placeholder="联系人电话🖋" type="text" v-model="d.model.contacterPhone" />
				</template>
			</my-cell>

			<my-cell label="收费补充"></my-cell>
			<textarea class="my-textarea light" v-model="d.model.paidIntro" style="height: 40px;"></textarea>

			<my-cell label="图片"></my-cell>
			
			<view class="" style="margin-top: 10rpx;">
				<my-upload-images provider="qn" path="venue" :max="6" v-model="d.model.images"></my-upload-images>
			</view>

		</view>
		
		<view class="page-bottom" >
			<button class="my-btn gray" @click="del" v-if="d.model.id && user.isAdmin">删除</button>
			<button class="my-btn success" @click="save">保存</button>
		</view>

	</view>
</template>

<script lang="ts" setup>
	import { onMounted, reactive } from "vue";
	import { onLoad } from "@dcloudio/uni-app"
	import { userStore } from '../../store/user'
	import { appStore } from '../../store';
	import ajax from "../../common/ajax";
	import { getLocByLL, wxContentCheck } from "../../common/funs";
	const user = userStore();
	const enums = appStore().enums;
	const d = reactive({
		cityPickerValueDefault: [0, 0, 0],
		id: '',
		model: {
			id: '',
			name: '',
			address: '',
			province:'',
			city:'',
			divisionCode:0,
			lat: '',
			lon: '',
			size: '',
			paid: '',
			images: [],
			able: "",
			type: '',
			parking: '',
			contacter: '',
			contacterPhone: '',
			createdAt: '',
			updatedAt: '',
			star: 0,
			isLike: false,
			user: {
				nickname: '',
				avatar: '',
				id: ''
			},
			static: {
				"visitCount": 0,
				"hotCount": 0,
				"likeCount": 0,
				"shareCount": 0,
			},
		} as any,
	})

	onMounted(()=>{
		if(!d.id){
			setDefaultLocation();
		}
	})

	onLoad(({ id } : any) => {
		if (id) {
			d.id = id;
			load(id)
		}

	})

	async function load(id : string) {

		let res = await ajax.get<any>('/venue/' + id)
		res.images = res.images ? res.images.split(',').map((v : any) => v) : [];
		res.type = res.type || res.buildType;
		if (res.lon) {
			// res.lon = parseFloat(res.lon)
			// res.lat = parseFloat(res.lat)
		}
		d.model = res;

	}
	async function setDefaultLocation() {

		d.model.lat = user.currentLocal.lat
		d.model.lon = user.currentLocal.lon

	}
	async function chooseLocation() {

		let res = await uni.chooseLocation({
			latitude: d.model.lat,
			longitude: d.model.lon,
		})
		if (res.errMsg != 'request:ok') {

			d.model.lat = res.latitude
			d.model.lon = res.longitude;
			d.model.address = res.address
			d.model.name ??= res.name;

			let { city, province,divisionCode } = await getLocByLL(d.model.lat, d.model.lon)

			changedCity([province, city],divisionCode)
		}

	}
	function changedCity(v : string[],divisionCode:string) {
		d.model.province = v[0]
		d.model.city = v[1]
		d.model.divisionCode=divisionCode;
	}
	// //确认地址
	// onConfirm(e){

	// 	if (e.label == "")
	// 	{
	// 		d.address = "省市区"
	// 	}
	// 	else{

	// 		var strs = e.label.split("-")
	// 		d.model.address = e.label
	// 		d.model.district = strs[2]
	// 		d.model.areaCode = e.areaCode
	// 		d.model.city = strs[1]
	// 		d.model.cityCode = e.cityCode
	// 		d.model.province = strs[0]
	// 		d.model.provinceCode = e.provinceCode
	// 	}
	// },
	async function save() {
		let error = ""
		if (!d.model.paid) {
			error = "请指明场地收费模式";
		}
		if (!d.model.address || !d.model.lon || !d.model.city || !d.model.divisionCode) {
			error = "请先选择（或填入）一下场地的具体位置";
		}
		if (!d.model.type) {
			error = "场地类型不能为空";
		}
		if (!d.model.size) {
			error = "规模不能为空";
		}
		if (!d.model.name) {
			error = "场地名称不能为空";
		}
		if (error) {
			uni.showToast({
				icon: 'none',
				title: error
			})
			return
		}
		await wxContentCheck(d.model.name + "," + d.model.intro)

		let postData = { ...d.model}
		postData.images = postData.images.map((v : any) => v.url).join(',');
		delete postData.user;
		delete postData.VenueStatic;
		await ajax.post('/venue', postData)
		uni.navigateBack()
	}
	async function del() {
		let res = await uni.showModal({
			title: '确认删除'
		});

		if (res.confirm) {
			await ajax.post(`/venue/${d.model.id}/del`)
			uni.navigateBack()
		}

	}
</script>

<style lang="scss">
	.map {
		box-shadow: 0 0 15upx rgba(0, 0, 0, .2);
		padding: 10upx;
		box-sizing: border-box;
		background-color: #fff;
	}
</style>