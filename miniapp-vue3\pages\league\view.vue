<template>
	<view class="page-container">
		<my-page-header title="赛事详情" :showBack="true" />
		<view class="league-card" :class="{active: d.league.state === 1}">
			<view class="state">
				<view class="iconfont icon-league2"></view>
				<text class="txt">[{{ enums.state[d.league.state] }}]</text>
			</view>
			<view>
				<view class="name">
					{{d.league.name}}
				</view>
				<view class="flex flex-center  mpt10 flex-gap10">
					<view class="flex flex-center f-color-gray ">
						<view class="iconfont icon-match "></view>
						{{ d.league.matchOverCount }} / {{ d.league.matchCount }}
					</view>
					<view class="flex flex-center f-color-gray">
						<view class="iconfont icon-time "></view>
						{{d.league.beginDate==d.league.endDate? formatDate(d.league.beginDate) : formatDate(d.league.beginDate) +" - "+  formatDate(d.league.endDate) }}
					</view>
				</view>

				<view class="intro mpt10" v-if="d.league.intro">
					{{ d.league.intro }}
				</view>
			</view>
		</view>




		<view class="matchs">

			<view class="match pd10" v-for="(item,idx) in d.matchs" :class="{active: item.state === 1}" :key="idx">
				<view class="mpt10 mpb10 flex flex-between flex-center f-color-white">
					<view class="flex flex-gap10 flex-center">
						<view class="iconfont icon-match f-lg"></view>
						<view class="" v-if="d.matchs.length > 1">
							第{{ idx + 1 }}场
						</view>
						<view class="">
							按出场顺序排名
						</view>

					</view>
					<view class="flex flex-center flex-gap10">
						<view v-if="d.matchs.length > 1" class="f-color-gray">{{ enums.state[item.state] }}</view>
						<navigator class="btn cy-right-arrow pd" :url="`/pages/match/match?id=${item.id}`">详情
						</navigator>
					</view>
				</view>
				<view >
					<view v-if="d.matchs.length > 1">
						<view>
							{{ item.name }}
						</view>
						<view class="iconfont icon-arrow-left"></view>
					</view>
					<view class="teams f-color-light">
						<view class="team" v-for="(t,idx2) in item.matchTeamResults" :key="idx2">
							<view class="name">
								{{ t.team.name }}
							</view>
							<view class="socre f-color-success">
								#{{ t.rank }}
							</view>
						</view>
					</view>
				</view>

			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
	import { reactive } from 'vue'
	import { onLoad, onShareAppMessage } from '@dcloudio/uni-app'
	import { appStore } from '../../store'
	import ajax from '../../common/ajax'
	import { formatDate } from '../../common/funs'
	import { userStore } from '../../store/user'

	const app = appStore()
	const enums = app.enums;

	const d = reactive({
		leagueId: '',
		league: {
			id: '',
			name: '',
			state: 0,
			beginDate: '',
			endDate: '',
			intro: '',
			matchOverCount: '',
			matchCount: '',
			Teams: []
		},
		matchs: [] as any[]
	})

	onLoad(async ({ id } : any) => {
		d.leagueId = id
		try {
			d.league = await ajax.get<any>(`/league/${d.leagueId}`)
			await loadMatchs()
		} catch (e) {
			console.error('加载联赛信息失败', e)
		}
	})

	onShareAppMessage(() => {
		const url = `/pages/league/view?id=${d.leagueId}`
		return {
			title: `毽球:${d.league.name}`,
			path: url
		}
	})

	function orderTeams(teams : any[]) {
		return teams.sort((a, b) => {
			let aRank = 0, bRank = 0
			try {
				aRank = parseInt(a.rank)
				bRank = parseInt(b.rank)
			} catch (e) { }
			return aRank > bRank ? 1 : -1
		})
	}

	async function loadMatchs() {
		try {
			const res = await ajax.get<any[]>('/match/query', {
				LeagueId: d.leagueId
			})
			res.forEach(v => {

				v.matchTeamResults = orderTeams(v.matchTeamResults)
			})
			d.matchs = res
		} catch (e) {
			console.error('加载比赛信息失败', e)
		}
	}
	function applyManager() {
		const user = userStore();
		uni.showModal({
			title: '隐藏事件',
			content: '提交绑定赛事管理员申请',
			success: (res) => {
				if (res.confirm) {
					ajax.post('/applyManager', {
						openid: user.info.openid,
						nickName: user.info.nickName,
						avatar: user.info.avatar,
						type: 110, // Admin: 999,Operator:910,//平台运维人员LeagueManager: 110,// 赛季维护 LeagueOperator:105//未用
						content: '赛季管理员:' + d.league.name + ":",
						applyTarget: d.league.id
					}).then(d => {
						uni.showToast({
							title: '申请成功，等待审批对应账号'
						})
					})
				} else if (res.cancel) {

					uni.showToast({
						icon: 'none',
						title: '没事别乱点：）'
					})

				}
			}
		});
	}
</script>

<style lang="scss" scoped>
	.league-card {
		padding: 18px;
		background: linear-gradient(-45deg, $uni-bg-color-grey 0, $uni-bg-color-hover 100%);
		border-radius: 12px;
		border: 1px solid #99999911;
		overflow: hidden;
		box-shadow: 4px 4px 4px #00000022;
		display: flex;
		justify-content: space-between;
		gap: 20px;

		.state {
			.iconfont {
				font-size: 80px;
			}

			color:$uni-text-color-grey;
			text-align:center;
		}

		.name {
			font-size: $uni-font-size-lg;
			font-weight: 500;
			margin-bottom: 10rpx;
			color: $uni-color-success
		}

		.info {

			.iconfont {
				font-size: 20px;
				margin-right: 10px;
			}
		}

		.intro {
			//color: #ffffff44;
		}
	}

	.leaguePage {}

	.matchs {
		//padding: 20rpx;
	}

	.match {
		.state {
			width: 60px;
			flex-shrink: 0;
			color: $uni-text-color-grey;
			text-align: center;

		}
	}

	.teams {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		gap: 10px 20px;
	}

	.team {
		width: 45%;
		justify-content: space-between;
		display: flex;
		padding: 4px 0;
		border-bottom: 1px solid rgba($uni-border-color, 0.1);

		.socre {
			//color:$uni-text-color-grey;
		}
	}
</style>