<template>
<view >
	<view class="flex flex-between">
		<view class="">
			<view class="f-title">
				hi,{{user.info.nickName}}
			</view>
			<view class="f-ext">
				Good morning.
			</view>
		</view>
		
		<view class="flex flex-center">
			<view class="btn cy icon hotpoint">
				<view class="iconfont icon-xiaoxi2">
				</view>
			</view>
			<view class="btn cy icon">
				<view class="iconfont icon-user">
				</view>
			</view>
		</view>

	</view>
</view>
</template>

<script lang="ts" setup>
	import { userStore } from '../store/user';
	const user = userStore();
</script>

<style>
</style>