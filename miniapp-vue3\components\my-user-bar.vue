<template>
<view >
	<view class="flex flex-between">
		<view class="">
			<view class="welcome">
				<text class="f-color-success">hi,{{user.info.nickName}} </text> 
				<text class="f-sm mpl10"> {{ greeting[0] }}</text>
			</view>
			<view class="f-ext">
				{{greeting[1]}}
			</view>
		</view>
		
		<view class="flex flex-center">
			<view class="btn cy icon hotpoint">
				<view class="iconfont icon-xiaoxi2">
				</view>
			</view>
			<view class="btn cy icon">
				<view class="iconfont icon-user">
				</view>
			</view>
		</view>

	</view>
</view>
</template>

<script lang="ts" setup>
	import { ref, onMounted } from 'vue';
	import { userStore } from '../store/user';
	import ajax from '../common/ajax';

	const user = userStore();
	const greeting = ref(['','---']);

	// 通过 AJAX 获取问候语
	const fetchGreeting = async () => {
		try {
			const result = await ajax.get<string[]>('/common/greeting');
			greeting.value = result;
		} catch (error) {
			console.error('获取问候语失败:', error);
			// 如果接口失败，使用本地计算的问候语作为备用
			const h = new Date().getHours();
			if(h<6){
				greeting.value[0] = '凌晨好';
			} else if(h<12){
				greeting.value[0] = '上午好';
			} else if(h<18){
				greeting.value[0] = '下午好';
			} else {
				greeting.value[0] = '晚上好';
			}
		}
	};

	// 组件挂载时获取问候语
	onMounted(() => {
		fetchGreeting();
	});
</script>

<style scoped>
.welcome{
	font-size: 1rem;
	font-weight: 500;
}
.f-ext{
	font-size: .8rem;
	color: #999;
}
</style>