@font-face {
  font-family: "iconfont"; /* Project id 4958781 */
  src: url('//at.alicdn.com/t/c/font_4958781_jrwma543heb.woff2?t=1754465090697') format('woff2'),
       url('//at.alicdn.com/t/c/font_4958781_jrwma543heb.woff?t=1754465090697') format('woff');
}

.iconfont {
  font-family: "iconfont" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
	 vertical-align: middle;
	 display: flex;
	 justify-content: center;
	 align-items: center;
 
}

.icon-ghost:before {
  content: "\e6c4";
}


.icon-team2:before {
  content: "\e62e";
}

.icon-medal:before {
  content: "\e7cd";
}
.icon-medal-123:before {
  content: "\e6ff";
}

.icon-date-time:before {
  content: "\e60f";
}

.icon-match:before {
  content: "\e658";
}

.icon-time:before {
  content: "\e695";
}

.icon-hot:before {
  content: "\e65a";
}
.icon-phone:before {
  content: "\e73a";
}

.icon-like:before {
  content: "\e663";
}

.icon-love:before {
  content: "\e665";
}

.icon-share:before {
  content: "\e66b";
}

.icon-nav:before {
  content: "\e672";
}

.icon-add:before {
  content: "\e606";
}

.icon-calendar:before {
  content: "\e604";
}

.icon-venue:before {
  content: "\e609";
}

.icon-star:before {
  content: "\e623";
}

.icon-star-fill:before {
  content: "\e624";
}

.icon-xiaoxi2:before {
  content: "\e605";
}

.icon-home:before {
  content: "\e680";
}

.icon-right-arrow:before {
  content: "\e76c";
}

.icon-setting:before {
  content: "\e64e";
}

.icon-msg:before {
  content: "\e621";
}

.icon-right-arrow2:before {
  content: "\e601";
}

.icon-league:before {
  content: "\e62d";
}

.icon-user:before {
  content: "\e602";
}

.icon-location:before {
  content: "\e603";
}

.icon-league2:before {
  content: "\e76b";
}
