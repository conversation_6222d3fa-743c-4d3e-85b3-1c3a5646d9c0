<template>
	<view class="cityTeam-list-page">
		<my-page-header title="团队列表" :showBack="true" />

		<view class="content">
			<view class="test-section">
				<text class="section-title">测试团队详情页</text>
				<button class="test-btn" @tap="goToTestDetail">查看测试团队详情</button>
			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
// 测试跳转到团队详情页
function goToTestDetail() {
	uni.navigateTo({
		url: '/pages/cityTeam/edit?id=1'
	});
}
</script>

<style lang="scss" scoped>
.cityTeam-list-page {
	min-height: 100vh;
	background-color: $uni-color-primary;
}

.content {
	padding: 20rpx;
}

.test-section {
	background-color: $uni-bg-color-grey;
	border-radius: 20rpx;
	padding: 40rpx;
	text-align: center;

	.section-title {
		display: block;
		font-size: 32rpx;
		color: $uni-text-color;
		margin-bottom: 30rpx;
	}

	.test-btn {
		background-color: $uni-color-success;
		color: $uni-color-primary;
		border: none;
		border-radius: 25rpx;
		padding: 20rpx 40rpx;
		font-size: 28rpx;
		font-weight: 600;
	}
}
</style>
