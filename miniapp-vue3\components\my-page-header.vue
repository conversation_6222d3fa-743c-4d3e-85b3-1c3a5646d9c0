<template>
	<view :style="{paddingTop:menu.top+'px',height:menu.height+menu.top+'px'}" class="page-header" style="
	">
		<view class="">
			<view v-if="showBack" 
				class="iconfont icon-right-arrow"
				style="transform: rotate(180deg);font-size: 22px;font-weight: 800;" 
				@tap="goBack">
			</view>
		</view>
		<view class="flex flex-center">
			<slot>
				<div class="_title" >{{props.title}}</div>
			</slot>
		</view>
		<view></view>
	</view>
	<view :style="{height:(navHeight+6)+'px'}"></view>
</template>
<style lang="scss" scoped>
	.page-header{
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding-left: 10px;
		padding-right: 10px;
		position: fixed;
		width: 100vw;
		z-index: 9;
		left: 0;
		background-color: $uni-color-primary;
	}
	._title{
		position: relative;
		font-size:32upx;
		font-weight: 600;
		text-align: center;
	}
</style>
<script lang="ts" setup>
var menu = uni.getMenuButtonBoundingClientRect()
var sys = uni.getWindowInfo();
const navHeight = menu.height + menu.top
const allHeight = menu.top+navHeight+6;
	// const sys = appStore()
const props = defineProps(['title','showBack'])

function goBack(){
 let routes = getCurrentPages();
	  if (routes.length == 1 && props.showBack) {
	    uni.switchTab({
	      url: '/pages/index/index'
	    })
	  } else{
		  uni.navigateBack()
	  }
}
</script>


