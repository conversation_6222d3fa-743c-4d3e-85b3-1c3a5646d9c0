<!--
标签选择

<my-switch size="sm"  :source="enums.venuePaid" v-model="model.paid"></my-switch>

model:
	v-model

大小(size):
	sm || mini

数据格式(d): 
	{1:'aaa',2:'bbb':'ccc'}
-->
<template>
	<view class="my-switch-group" :class="size">
		<view class="my-switch-item" @tap="changed(key,idx)" :key="idx" :class="{selected:sourceInclude(key)}" v-for="(val,key,idx) in props.source">{{val}}</view>
	</view>
</template>

<script setup lang="ts">
import { ref, watch, defineProps, defineEmits, withDefaults } from 'vue'



interface Props {
	modelValue?: number|null|undefined|''
	size?: string
	source: Record<number,string>
}

// 定义props with 类型和默认值
const props = withDefaults(defineProps<Props>(), {
	modelValue: '',
	size: '',
	source:()=>{return {}}
})

// 定义 emits
const emit = defineEmits(['update:modelValue', 'changed'])

// 响应式数据
const value2 = ref<number|null|undefined|''>('')

// 监听 modelValue 变化
watch(() => props.modelValue, (v: number | null | undefined|'') => {
	value2.value = v
}, { immediate: true })


const changed = (key: number, idx: number): void => {
	value2.value = (value2.value as number) ^ key
	emit('update:modelValue', value2.value)
	emit('changed', value2.value)
}

function sourceInclude(vals:number){
	return ((value2.value as number) & vals) !==0
}
</script>

<style lang="scss" scoped>
	.my-switch-group {
		.my-switch-item {
			display: inline-block;
			line-height: 2;
			background: $uni-bg-color-grey;
			border: 1upx solid $uni-border-color;
			text-align: center;
			padding: 0 20upx;

			&:first-of-type {
				border-radius: 4px 0 0 4px;
				border-right: 0;
			}

			&:last-of-type {
				border-radius: 0 4px 4px 0;
				border-left: 0;
			}

			&.selected {

				border-color: $uni-text-color-inverse;
				background: $uni-color-success;
				color: $uni-color-primary
			}
			
		}
		&.warn {
			&>view{
				&.selected {
					border-color: $uni-color-warning;
					background: $uni-color-warning;
					color: $uni-text-color-inverse;
					
				}
			}
		}
		&.sm {
			&>view {
				font-size: .85em;
			}
		}

		&.mini {
			&>view {
				font-size: .7em;
			}
		}
	}
</style>
