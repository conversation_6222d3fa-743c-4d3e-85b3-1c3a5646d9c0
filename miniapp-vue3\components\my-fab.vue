<template>
	
		
		<view class="fab-cart" >
			<navigator :url="url">
			<slot>
				<view class="flex-center-all">
					<view style="line-height: 1.2; margin-top: 5px;">
						<view class="f-sm f-color-gray">
							发起
						</view>
						<view class="f-color-success f-sm iconfont icon-add f-b"></view>
					</view>
				</view>
			</slot>
			</navigator>
		</view>

</template>
<script setup lang="ts">
	const props = defineProps(["url"])
</script>
<style lang="scss" scoped>
navigator{
	padding: 0;
}
.fab-cart{
		position: fixed;
		box-sizing: border-box;
		bottom: 10vh;
		right: 10vw;
		border-radius: 80upx;
		border: 1px solid $uni-color-primary;
		width: 80upx;
		height: 80upx;
		background:linear-gradient(90deg,$uni-bg-color 0%, $uni-bg-color-grey 100%);
		text-align: center;
		box-shadow: 0 0 10upx rgba(0,0,0,.5);
		overflow: hidden;
		z-index: 2;
		// color: $uni-color-success;
		.default{
			position: relative;
			opacity: .7;
			&:before,
			&:after{
				position: absolute;
				width: 6upx;
				height: 40upx;
				background-color: rgba(255,255,255,1);
				display: block;
				border-radius: 4upx;
				content: "";
				transform-origin: center center;
				top:15upx;
				left: 32upx;
			}
			&:after{
				transform: rotate(90deg);
			}
		}
	}
</style>
