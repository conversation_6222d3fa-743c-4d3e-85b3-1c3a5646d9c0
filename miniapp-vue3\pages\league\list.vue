<template>
	<view>
		<my-page-header title="毽球赛事" />
		<view class="leagues" v-if="d.leagues.length !== 0">
			<navigator class="league" :class="{active: item.state === 1 || item.state === 0}" v-for="item in d.leagues"
				:key="item.id" :url="`/pages/league/view?id=${item.id}`">
				<view class="name">{{ item.name }} <text class="txt">{{enums.state[item.state]}}</text></view>

				<view class="info">
					<view class="">
						<view class="flex flex-center">
							<view class="iconfont icon-match "></view>
							{{ item.matchOverCount }} / {{ item.matchCount }}
						</view>
						<view class="flex flex-center">
							<view class="iconfont icon-time "></view>
							{{item.beginDate==item.endDate? formatDate(item.beginDate) : formatDate(item.beginDate) +" - "+  formatDate(item.endDate) }}
						</view>
					</view>

					<view class="intro" v-if="item.intro">
						{{ item.intro }}
					</view>

					<text class="iconfont icon-arrow-left arrowEnter"></text>
				</view>

					<view class="state">
						<view class="iconfont icon-league2"></view>
					</view>
			</navigator>
		</view>
		<view v-else class="errorMsg">
			{{ d.errorMsg }}
		</view>
	</view>
</template>

<script lang="ts" setup>
	import { reactive } from 'vue'
	import { onLoad, onShareAppMessage, onShareTimeline } from '@dcloudio/uni-app'
	import { appStore } from '../../store'
	import ajax from '../../common/ajax'
	import { formatDate } from '../../common/funs'

	const app = appStore()
	const enums = app.enums

	const d = reactive({
		leagues: [] as any[],
		errorMsg: ''
	})

	onLoad(async () => {
		d.errorMsg = ''
		try {
			const leagues = await ajax.get<any[]>('/league')
			if (!leagues || leagues.length === 0) {
				d.errorMsg = '暂时还没有历史赛事'
			}
			d.leagues = leagues || []
		} catch (e) {
			d.errorMsg = '加载失败'
		}
	})

	onShareTimeline(() => {
		return {
			title: '毽球风云:历史赛事',
			query: ''
		}
	})

	onShareAppMessage(() => {
		const url = '/pages/league/list'
		return {
			title: '毽球风云:历史赛事',
			path: url
		}
	})
</script>

<style lang="scss" scoped>
	.leagues {
		display: flex;
		flex-wrap: wrap;
		min-height: 70vh;
		justify-content: center;
		align-items: center;
		align-content: center;
		gap: 12px;
	}

	.league {
		
		width: 44%;
		position: relative;
		padding: 18px;
		background-color: $uni-bg-color-grey;
		// background: linear-gradient(-45deg, $uni-bg-color-grey 0, $uni-bg-color-hover 100%);
		// box-shadow: 4px 4px 4px #00000022;
		border-radius: 12px;
		border: 1px solid #99999911;
		height: 240px;
		overflow: hidden;

		display: flex;
		flex-direction: column;
		//background:linear-gradient(0, rgba(255, 255, 255, .05) 1px, transparent 0);
		// &:before {
		// 	content: "";
		// 	display: inline-block;
		// 	top: 30rpx;
		// 	position: absolute;
		// 	left: 25rpx;
		// 	width: 80rpx;
		// 	background: #e6ab3d;
		// 	height: 5rpx;
		// 	transform: rotate(-18.5deg);
		// }
		.intro{
			color: #ffffff38;
			// border-top: 1px solid #ffffff38;
			padding-top: 10px;
			position: relative;
			&:before{
				content: "";
				display: block;
				position: absolute;
				top: 0;
				left:0;
				width: 100%;
				height: 1px ;
				background-color: #ffffff38;
				// background:linear-gradient(90deg,transparent 0%, #ffffff38 40%,#ffffff38 60%, transparent 100%);
			}
			
		}
		.state {
			position: absolute;
			right: -50%;
			top:15%;
			.iconfont {
				font-size: 160px;
				color: #ffffff05;
			}

		}
		.txt{
			font-size: 14px;
			color: $uni-text-color-grey;
		}
		.info {
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			gap: 10px;
			color: $uni-text-color-grey;
			.iconfont {
				font-size: 18px;
				margin-right: 10px;
			}
		}

		.name {
			font-size: $uni-font-size-lg;
			font-weight: 500;
			margin-bottom: 10rpx;
			    flex-shrink: 0;
			height: 80px;
			overflow: hidden;
		}



		&.active {
			//background:linear-gradient(0, rgba(255, 255, 255, .2) 10px, transparent 0);
			.name{
				color: $uni-color-success;
			}
			.state {
				
				.iconfont {
					color: #ff9900;
				}
			}
			.txt{
				color: #ff9900;
			}
		}
	}

	.arrowEnter {
		position: absolute;
		right: 10rpx;
		font-size: 120%;
		top: 50%;
		margin-top: -10rpx;
		opacity: .6;
	}

	.navigator-hover {
		background:
			linear-gradient(-198.5deg, $uni-bg-color-grey 0, $uni-bg-color-hover 100%),
		;
	}
</style>