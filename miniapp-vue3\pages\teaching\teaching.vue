<template>
	<view class="body teachingPage" :style="{'background-image':$asset('bg','bg')}">
		<my-page-header title="毽球教程" />
		<view class="body-flex">
			<scroll-view class="documents" :scroll-y="true" style="height: calc(100vh - 300rpx);">
				<official-account></official-account>
				<view class="document" v-for="item in d.data.list" :key="item.id" @click="go(item)">
					<view class="document-content">
						<view class="title nowrap">
							{{ item.name }}
						</view>
						<view class="intro">
							{{ item.intro || "" }}
						</view>
					</view>
					<image lazy-load class="document-avatar" mode="aspectFill" :src="item.avatar"></image>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script lang="ts" setup>
import { reactive } from 'vue'
import { onLoad, onShareAppMessage } from '@dcloudio/uni-app'
import ajax from '../../common/ajax'

const d = reactive({
	data: {
		list: [] as any[]
	}
})

onLoad(() => {
	load()
})

onShareAppMessage(() => {
	return {
		title: '毽球风云:毽球教程',
		path: '/pages/teaching/teaching'
	}
})

async function load() {
	try {
		const res = await ajax.get<any>('/documents', { where: { groupKey: 'teaching' } })
		d.data = res
	} catch (e) {
		console.error('加载教程失败', e)
	}
}

function go(item: any) {
	if (item.extUrl) {
		uni.navigateTo({
			url: `/pages/WebView/WebView?url=${item.extUrl}`
		})
	} else {
		uni.navigateTo({
			url: `/pages/Document/Document?id=${item.id}`
		})
	}
}
</script>

<style scoped lang="scss">
body{
	overflow: hidden;
}
.teachingPage{
	display: flex;
	flex-direction: column;
	
	.documents{
		padding-top: 0rpx;
		padding-right:20px;
		.document{
			margin: 20rpx 0;
			padding: 20rpx;
			background:rgba(255,255,255,.2);
			box-shadow:10rpx 10rpx 0 rgba(0,0,0,.2);
			display:flex;
			.document-content{
				flex-basis: 510rpx;
				flex-grow:0;
				.title{
					font-size: 100%;
					line-height: 100%;
				}
				.intro{
					overflow: hidden;
					padding-top:10rpx;
					font-size: 80%;
					max-height: 120rpx;
					color: rgba(255,255,255,.7);
					text-overflow: ellipsis;
				}
			}

			.document-avatar{
				flex-basis: 160rpx;
				width:120rpx;height:120rpx;
				border:1px solid rgba(255,255,255,.1);
			}
		}
	}
}
</style>
