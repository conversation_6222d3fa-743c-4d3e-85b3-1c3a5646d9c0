<template>
	<view class="page-imgbg documentPage" :style="{'background-image':$asset('bg','bg')}">
		<sheader title="毽球教程"></sheader>
		<view class="">
			<official-account></official-account>
		</view>
		<view class="title">
			{{data.name}}
		</view>
		<view class="intro">
			{{data.intro}}
		</view>
		<view class="content">
			<rich-text :nodes="data.content" ></rich-text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				data:{
					name:'',
					intro:'',
					content:''
				}
			};
		},
		onShareAppMessage() {
		  return {
		    title: '毽球风云:毽球教程',
		    path: "/pages/Teaching/Teaching",
		    success: function(res) {
		      res.shareTickets
		    },
		    fail: function(res) {
		      console.log(res)
		    }
		  }
		},
		onLoad(param){
			this.load(param.id)
		},
		methods:{
			load(id){
				this.$ajax.get('/document',{id}).then(d=>{
					this.data = d;
				})
			},

		}
	}
</script>

<style lang="scss">
.documentPage{
	background: red;
	.title{
		font-size: 120%;
		text-align: center;
		padding: 20rpx;
	}
	.intro{
		padding: 20rpx;
		font-size: 100%;
		max-height: 120rpx;
		color: rgba(255,255,255,.7);
	}
	.content{
		padding: 20rpx;
		font-size: 80%;
		color: rgba(255,255,255,.7);
	}
}
</style>
