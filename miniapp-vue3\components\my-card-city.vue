<template>
	<view class="card flex flex-between" style="color: #f0f0f0;padding: 20px;">
		<template v-if="isLoaded">
		<view class="" >
			<view class="g-gray f-sm">
				场地 >
			</view>
			<view  >
				{{d.venueCount}} 个
			</view>
			<view class="mpt10 g-gray f-sm">
				团队 >
			</view>
			<view >
				{{d.cTeamCount}} 个
			</view>
			<view class="mpt10 g-gray f-sm">
				注册人数 >
			</view>
			<view class="">
				{{d.memberCount}} 个
			</view>
		</view>
		<view class="">
			<view class="">
				{{d.province}}
			</view>
			<view class="f-lgx2">
				{{d.city}} >
			</view>
			<view style="padding-top: 10px;">
				<view class="my-btn success cy" style="width: 120px;">
					切换城市
				</view>
			</view>
		</view>
		</template>
		<template v-else>
			<div class="flex flex-center"></div>
			数据等待中
		</template>
	</view>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from 'vue';
import { userStore } from '../store/user';
import ajax from '../common/ajax';
const d = ref<ICityCardData>({
	province:'',
	city:'',
	cityCode:0,
	cTeamCount:0,
	venueCount:0,
	memberCount:0
})
const isLoaded = ref(false)
const us = userStore();
watch(()=>us.tryLocal.divisionCode,(n,o)=>{
	if(n&& n!=o){
		load();
	}
		
},{immediate:true})
async function load(){
	const res = await ajax.get<ICityCardData>('/city/card',{divisionCode:us.tryLocal.divisionCode});
	isLoaded.value=true;
	d.value = res;
	d.value.province = us.tryLocal.province;
	d.value.city = us.tryLocal.city;
}
const cityCardMap:ICityCardData[]=[]

const currentCityData = computed(v=>{
	
})

</script>

<style>

</style>