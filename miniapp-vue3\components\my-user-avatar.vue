<template>
	<view class="avatar" :class="`${size} ${nameLine!==false?'lineLayout':''}`" @tap="goUser">
		<view class="avatar-icon">
			<image v-if="data.avatar" class="avatar-img" lazy-load :src="data.avatar+'?imageView2/1/w/100/h/100'"
				mode="aspectFill"></image>
			<view v-else class="default-icon" :class="defaultIcon"></view>
		</view>
		<view class="name" v-if="props.showName!==false">
			{{data.nickName}}
		</view>
	</view>
</template>

<script lang="ts" setup>
	import { ref } from 'vue';
	const props = withDefaults(defineProps<{
		showName ?: boolean,
		allowLink ?: boolean,
		nameLine ?: boolean
		defaultIcon ?: string
		data : {
			avatar ?: string,
			nickName ?: string
		},
		size : "mini" | "sm" | "big" | ""

	}>(), {
		showName: false,
		allowLink: false,
		nameLine: false,
		size: "",
		defaultIcon: 'iconfont icon-ghost',
		data: () => {
			return {
				avatar: '',
				nickName: ''
			}
		}
	});

	function goUser(id : string) {
		if (props.allowLink !== false) {
			uni.navigateTo({ url: '/pages/user/user?id=' + id })
		}

	}
</script>
<style lang="scss">
	.avatar {
		display: inline-block;
		text-align: center;

		&.lineLayout {
			display: flex !important;
			flex-direction: row;
			align-items: center;
			gap: 6px;
		}

		.avatar-icon {
			width: 100%;
			height: 100%;
			overflow: hidden;
			position: relative;
			border-radius: 500px;
			width: 48px;
			height: 48px;
			background-color: #aaa;
			.avatar-img {
				width: 100%;
				height: 100%;
			}

			.default-icon {
				font-size: 32px;
				line-height: 48px;
				color: #ffffff;
			}

			&::after {
				content: "";
				position: absolute;
				left: 0;
				top: 0;
				width: 100%;
				height: 100%;
				border: 2px solid #fff;
				border-radius: 500px;
			}
		}

		.name {
			ine-height: 2;
		}
		
		&.mini {
			.avatar-icon {
				width: 28px;
				height: 28px;
				&::after {
					border: 1.5px solid #fff;
				}
				.default-icon{
					font-size: 20px;
					line-height: 28px;
				}
			}
		}

		&.sm {
			.avatar-icon {
				width: 32px;
				height: 32px;
				.default-icon{
					font-size: 28px;
					line-height: 32px;
				}
			}
		}

		&.big {
			.avatar-icon {
				width: 60px;
				height: 60px;
				.default-icon{
					font-size: 36px;
					line-height: 60px;
				}
			}
		}
	}
</style>