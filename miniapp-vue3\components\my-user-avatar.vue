<template>
	<view class="avatar size" :class="`${size} ${isLineLayout!==false?'lineLayout':''}`" @tap="goUser">
		 <image class="avatar-icon" lazy-load :src="(data.avatar||defaultAvatar)+'?imageView2/1/w/100/h/100'" mode="aspectFill"></image>
		 <view class="name" v-if="props.showName!==false">
		 	{{data.nickName}}
		 </view>
	</view>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
const props = defineProps<{
	showName?:boolean,
	allowLink?:boolean,
	isLineLayout?:boolean
	data:{
		avatar?:string,
		nickName?:string
	},
	size:"mini"|"sm"|"big"|""

}>();
import defaultAvatar from "../static/images/default-avatar.png"

function goUser(id : string) {
	if(props.allowLink!==false){
		uni.navigateTo({ url: '/pages/user/user?id=' + id })
	}
	
}
</script>
<style lang="scss">
	.avatar{
		display: inline-block; text-align: center;
		&.lineLayout{
			display: flex !important;
			flex-direction: row;
			align-items: center;
			gap: 6px;
		}
		.avatar-icon{
			width: 100%;
			height: 100%;
		}
		.name{
			 ine-height: 2;
		}
		&.size{
			.avatar-icon{
				border:2px solid #fff;
				border-radius:1000px;
				overflow: hidden;
				width: 48px;
				height: 48px;
			}
			&.mini{
				.avatar-icon{
					width: 28px;
					height: 28px;
					border-width: 1.5px;
				}

			}
			&.sm{
				.avatar-icon{
					width: 32px;
					height: 32px;
				}
			}
			&.big{
				.avatar-icon{
				width: 80px;
				height: 80px;
				}
			}
		}

	}

</style>