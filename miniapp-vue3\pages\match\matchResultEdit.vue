<template>
	<view class="body matchPage matchResultEditPage" :style="{'background-image':$asset('bg','bg')}">
		<my-page-header :title="d.match.League.name" :showBack="true" />
		<view style="padding: 32rpx;">
			<view class="match-state" :class="{active: d.match.state === 1}">
				<view class="iconfont iconmatch"></view>
				<view class="txt">[{{ enums.state[d.match.state] }}]</view>
			</view>
			
			<view class="match-intro">
				<view class="name">
					{{ d.match.name }}
				</view>
				<view class="item">
					<view class="iconfont icontime"></view> {{ formatDate(d.match.beginDate) }} - {{ formatDate(d.match.endDate) }}
				</view>
				<view class="item">
					<view class="iconfont iconaddress"></view> {{ d.match.address || "" }}
				</view>
				<view class="item">
					<view class="iconfont iconmsg"></view> {{ d.match.intro || "" }}
				</view>
				<view class="class">
					[ <text>{{ enums.matchType[d.match.type] }}</text> ]
					[ <text>{{ d.match.MatchClass.name }}</text> ]
				</view>	
			</view>

			<view class="results">
				<view class="resultIdxType" @click="d.idxType = (d.idxType === 1 ? 0 : 1)">
					{{ d.idxTypes[d.idxType] }}
					<text class="iconfont icondown"></text>
				</view>
				<view class="result" v-for="(item,idx) in resultByIdx" :key="idx">
					<view class="teamIdx">{{ item.rank }}</view>
					<view class="teamName">
						{{ item.Team.name }}
					</view>
					<view class="score">得分：
						<input type="digit" class="score-input" v-model="item.score"/>
						<button class="saveBtn" :disabled="d.waiting" @click="updateScore(item)">更新</button>
					</view>
					<view class="sign">
						<view v-if="item.score != 0" class="scoreIdx">{{ item.scoreIdx }}</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
import { reactive, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { appStore } from '../../store'
import { userStore } from '../../store/user'
import ajax from '../../common/ajax'
import { formatDate } from '../../common/funs'

const app = appStore()
const user = userStore()
const enums = app.enums

const d = reactive({
	idxType: 0,
	idxTypes: {
		0: '序号',
		1: '比分'
	},
	match: {
		id: '',
		name: '',
		state: 0,
		beginDate: '',
		endDate: '',
		address: '',
		intro: '',
		type: 0,
		League: {
			id: '',
			name: ''
		},
		MatchTeamResults: [],
		MatchClass: {
			name: ''
		}
	},
	model: {
		id: '',
		score: ''
	},
	waiting: false
})

const resultByIdx = computed(() => {
	let _results = [...d.match.MatchTeamResults]
	_results.sort((a, b) => {
		if (a.score == b.score) {
			return parseInt(a.rank) > parseInt(b.rank) ? 1 : -1
		}
		return a.score >= b.score ? -1 : 1
	})
	_results.map((v, idx) => {
		v.scoreIdx = idx + 1
	})
	
	if (d.idxType == 0) {
		_results.sort((a, b) => {
			return parseInt(a.rank) > parseInt(b.rank) ? 1 : -1
		})
	}
	return _results
})

onLoad(async (param: any) => {
	await load(param.id)
})

async function load(id: string) {
	try {
		const match = await ajax.get<any>(`/match/${id}`)
		d.match = match
	} catch (e) {
		console.error('加载比赛信息失败', e)
	}
}

async function updateScore(item: any) {
	d.waiting = true
	try {
		await ajax.post('/updateMatchResultScore', {
			resultId: item.id,
			openid: user.info.openid,
			score: item.score
		})
		uni.showToast({
			title: '更新成功'
		})
	} catch (e) {
		console.error('更新分数失败', e)
		uni.showToast({
			title: '更新失败',
			icon: 'none'
		})
	} finally {
		setTimeout(() => {
			d.waiting = false
		}, 1000)
	}
}
</script>

<style scoped lang="scss">
	.matchResultEditPage{
		filter:hue-rotate(50deg); //sepia(50%); //grayscale(20%) 
	}
	.score{
		width: 240rpx !important;
	}
	.score-input{
		display: inline-block;
		vertical-align: middle;
		margin-right: 10rpx;;
		width: 100rpx;
		font-size: 120%;
		background: rgba(255,255,255,.3);
	}
	.teamIdx{
		vertical-align: middle;
	}
	.teamName {
		line-height: 65rpx;
	}
	.saveBtn{
		font-size: 24rpx;
		padding: 1rpx 6rpx;
		display: inline-block;
		vertical-align: middle;
		background: #4CD964;
		color: #fff;
	}
</style>