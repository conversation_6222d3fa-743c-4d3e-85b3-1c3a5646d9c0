<template>
	<myScrollView @overTop="load(true)" @overBottom="upload()" :upStatus="upStatus" :downStatus="downStatus" ref="msv">
		<template v-if="$slots.beforSlot">
			<slot name="beforSlot" :scope="scope"/>
		</template>
		<view style="text-align: center;padding: 100upx;" v-if="!$slots.beforSlot &&  loaded && datas.list.length==0">
			暂无数据
		</view>
		<list style="items">
			<view v-for="(item, index) in datas.list" :key="index">
				<slot name="item" :item="item" :_index="index" :scope="scope"></slot>
			</view>
		</list>
	</myScrollView>
</template>

<script>
	import ajax from "../common/ajax";
import myScrollView from "@/components/my-scrollview.vue"
	export default {
		components:{
			myScrollView	
		},
		data() {
			return {
				datas:{
					list:[],
					pageKey:'',
					pageIndex:0,
					total:0,
					isEnd:false
				},
				upStatus:'',
				downStatus:'',
				loaded:false,
				props2:{
					pageIndex:'pageIndex',
					pageSize:'pageSize',
					pageKey:'pageKey',
					total:'total',
					list:'list',
					isEnd:'isEnd'
				}
			};
		},
		wacth:{
			pageSize:{
				immediate:true,
				handler(v){
					this.datas.pageSize = v;
				}
			},
				
			props:{
				immediate:true,
				handler(){
					this.props2 = Object.assign(this.props2,this.props)
					//this.datas
				}
			}
			
		},
		created(){
			if(this.autoLoad!==false){
				this.load(true)
				
			}
		},
		methods:{

			tryFirstLoad(){
				if(this.datas.list.length==0){
					this.load(true)
				}
			},
			upload(){
				if(this.upStatus){
					this.load()
				}
			},
			getData(){
				return this.datas;
				
			},
			addList(items,idx=0){
				items.forEach(v=>{
					this.datas.list.splice(idx,0,v);
				})
				this.datas.total +=items.length;
				this.$emit('changed',this.getData())
			},
			remove(idx){
				let item = this.datas.list[idx];
				item.isDel = true;
				this.datas.list.splice(idx,1)
				this.datas.total -=1
				this.$emit('changed',this.getData())
			},
			async load(isForce=false){
				
				if(!this.url){
					this.$emit('load',{isForce})
					return
				}
				
				if(isForce){
					this.downStatus = 'loading'
					this.datas[this.props2.pageKey]="";
					this.loaded=false;
					this.datas[this.props2.pageIndex]=0
				}else{
					this.upStatus = 'loading'
				}
				
				let queryData = {}
				

				if(this.datas[this.props2.pageKey]){
					queryData[this.props2.pageKey] = this.datas[this.props2.pageKey];
				}else{
					queryData[this.props2.pageIndex] = this.datas[this.props2.pageIndex]+1;
				}
				queryData[this.props2.pageSize] = this.pageSize;

				if(this.params){
					
					queryData = Object.assign(queryData,this.params)
				}
				
				for(const k in queryData){
					if(queryData[k]==="" || queryData[k]===undefined || queryData[k]===null){
						delete queryData[k];
					}
				}
				
				let res ;
				try{
					res = await ajax.get(this.url,queryData);
					
				}
				catch(e){
					console.log('e',e)
					this.upStatus = ''
					this.downStatus = ''
					return
				}
				finally{
					setTimeout(()=>{
						this.loaded=true;
					},500)
				}

				
				if(this.transf){
					res.list = res.list.map(v=>{
						return this.transf.bind(this.$parent)(v)
					})
				}
				
				if(isForce){
					this.datas[this.props2.list] = res[this.props2.list];
					this.$refs.msv.toTop()
				}else{
					this.datas[this.props2.list]= this.datas[this.props2.list].concat(res[this.props2.list])
				}
				
				this.datas[this.props2.pageIndex] = res[this.props2.pageIndex]
				
				this.datas.total=res.total;
				
				this.datas[this.props2.pageKey] = res[this.props2.pageKey];
				
				//判断是最后一页
				if(res.isEnd || res.isLastPage || res[this.props2.list].length<this.pageSize){
					this.upStatus = ''
				}else{
					this.upStatus = 'more'
				}
				
				this.downStatus = ''
				
				this.$emit('loaded',this.getData())
				this.$emit('changed',this.getData())
				console.log('upStatus =>'+this.upStatus)
				
			},

		},
		props:{
			url:'',
			params:{
				type:Object,
				default:function(){
					return {}
				}
			},
			//解决小程序 slot-scope时，渲染阶段 无法穿透到父域，获取父域在变量
			//将需要传递的变量/对象，交给root，在slot 之slot-scope={item,$index,scope}获取使用
			scope:{
				type:Object,
				default:function(){
					return null
				}
			},
			transf:null,
			autoLoad:true,
			pageKey:true,
			pageSize:{
				type:Number,
				default:function(){
					return 10
				}
			},
			props:{
				type:Object,
				default:function(){
					return {
						pageIndex:'pageIndex',
						pageSize:'pageSize',
						total:'total'
					}
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.items{
		// height: 100%;
		-webkit-overflow-scrolling: touch;
		min-height: 100%;
	}
</style>
