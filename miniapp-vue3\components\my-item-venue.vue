<template>

	<view class="item">

		<image v-if="item.images && item.images[0]" lazy-load :src="item.images[0] +'?imageView2/1/w/600/h/600'" class="headerImg" mode="aspectFill" ></image>

		<view class="flex flex-between">
			<view class="">
				<view class="">
					{{item.name}}
				</view>
				<view class="address" style="margin-top: 2px;">
					<view class="address-txt">
						{{item.address}}
					</view>
				</view>

			</view>
			<view class="f-sm">
				<view class="flex flex-gap5">
					<view class="iconfont icon-nav" style="color: #a7a7a7;"></view>
					<view class=" nowrap">{{nowDistance}}</view>
				</view>
				
				<view style="margin-top: 4px;" class="flex flex-gap5">
					<view class="iconfont icon-hot" :style="{
						color:item.static.hotCount>100?'#ef4324':(item.static.hotCount<20?'#a7a7a7':'#ff8a20')}"
					></view>
					<view class="">
						{{item.static.hotCount}}
					</view>
					
				</view>
			</view>
				

		</view>
		

		<view class="flex flex-between flex-center mpt10 ">
			<view class="flex flex-center flex-gap10" style="flex: 40%;">
				<my-user-avatar :data="item.user" size="mini"></my-user-avatar>
				<my-rate v-model="item.star" :max="5" color="rgba(255,255,255,.5)" active-color="#ff9d00" :size="20"/>
			</view>
			<view class="" style="text-align: right;">
				<view class="my-tag" v-if="item.size && item.size!='0'">
					{{app.tryEnum("venueSize",item.size) }}
				</view>
				<view class="my-tag warn" >
					{{app.tryEnum("venueType",item.type)}}
				</view>
				<view class="my-tag light" v-if="item.able">
					{{app.tryEnum("venueAble",item.able)}}
				</view>
				<view class="my-tag paid-tag" :class="'paid-tag-'+item.paid">
					{{app.tryEnum("venuePaid",item.paid)}}
				</view>
			</view>
		</view>
	</view>

</template>

<script  lang="ts" setup>
	import { computed } from 'vue';
import { appStore } from '../store';
import { getDistance } from '../common/funs';
import { userStore } from '../store/user';
const app = appStore();
const props  = defineProps(["item"])
const user = userStore();


const nowDistance = computed(()=>{
	if(user.currentLocal.lat && props.item){
		return getDistance(user.currentLocal.lat,user.currentLocal.lon,props.item.lat,props.item.lon).toFixed(1)+'公里';
	}
})


</script>

<style lang="scss">
	.item{
		padding: 12px;
		border-radius: 12px;
		background-color:$uni-bg-color-grey;
		overflow: hidden;
		margin-bottom: 5rpx;
		width: 100%;
		color: #fff;
		.headerImg{
			width: 100%;
			height: 240rpx;
			border: 1px solid rgba(255, 255, 255, 0.1);
			//outline: 4px solid rgba(0, 0, 0, 0.090);
			border-radius: 8px;
			position: relative;
			margin-bottom: 10rpx;
			&:before{
				content: "";
				display: block;
				position: absolute;
				width: 100%;
				height: 100%;
				left: 0;
				top: 0;
				background:radial-gradient(transparent 60%,#00000099)
			}
			&-null{
				background: rgba(56, 35, 95, 0.68);
				text-align: center;
				line-height: 240rpx;
				color:rgba(255, 255, 255, 0.3) ;
			}
		}
		.my-tag{
			font-size: $uni-font-size-sm;
			padding: 0 10rpx;
			line-height: 1.7;
		}
		.paid-tag{

			color: #fff;
			display: inline-block;
			&.paid-tag-1{
				background: rgba($uni-color-success,.5)
			}
			&.paid-tag-2{
				background: rgba($uni-color-error,.5)
			}
		}
		
		.address{
			align-items: flex-start;
			.address-icon{
				flex:0;
				width: 32upx;
				height: 32upx;
				line-height:32upx;
				opacity: .7;
			}
			.address-txt{
				flex-grow: 1;
				color: rgba(255,255,255,.5);
				font-size: $uni-font-size-base;
			}
		}
	}
	
</style>
