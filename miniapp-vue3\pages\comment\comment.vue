<template>
<z-paging ref="pagingRef" v-model="d.list" @query="load()">
	<template #top>
		<my-page-header title="评论" :showBack="true"></my-page-header>
	</template>
	<view class="item" v-for="(item,index) in d.list" :key="index">
		<view class="item-title">{{item.title}}</view>
	</view>
</z-paging>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue';
import ajax from '../../common/ajax';
import {onLoad} from "@dcloudio/uni-app"
const d = reactive({
	targetId:'',
	list:[] as any[]
})
const pagingRef= ref()
onLoad(({targetId})=>{
	d.targetId = targetId
})
async function load(pageIndex=0,pageSize:number=10) {
	if(!d.targetId)return;
	var res = await ajax.get<any>(`/comment/list?targetId:${d.targetId}`, { pageSize,pageIndex })
	pagingRef.value.complete(res.list);

}
</script>

<style>

</style>
