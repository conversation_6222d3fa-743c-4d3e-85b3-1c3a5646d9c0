import { defineStore } from 'pinia';
import ajax from "@/common/ajax"
import { getCurrentLoc, } from '../common/funs';
import { computed, ref, unref } from 'vue';
import { Defaultlocation } from '../common/config';
export const userStoreKey = '__user';
export const tokenStoreKey = '__token';

let defaultState = <{ info : IMyUser, account ?: IAccount, currentLocal? : ILocation}>uni.getStorageSync(userStoreKey)
let defaultTokenInfo= <{token:string, expire : string }>uni.getStorageSync(tokenStoreKey) || {token:'', expire : '' }
export const userStore = defineStore('user', ()=>{
	const info = ref(defaultState.info || {
		id:'',
		nickName: '',
		name: "",
		avatar: '',
		gender: '',
		province: '',
		city: '',
		county: '',
		divisionCode: 0,
		level: 0,
		phone: '',
		sign: '',
		isMember:false,
	})
	const tokenInfo = ref(defaultTokenInfo || {token:'', expire : ''});
	const account = ref({ type: 0, name: "" })
	const currentLocal = ref<ILocation>({ lon: 0, lat: 0, province: '', city: '', area: '', divisionCode: 0 });

	const isLogin = computed(() => {
		return tokenInfo.value.token ? true : false;
	})
	const isMember = computed(() => {
		return info.value.divisionCode ? true : false;
	})
	
	const isAdmin = computed(() => {
		return account.value.type>900 ? true : false;
	})
	
	const tryLocal = computed(():ILocation=>{
		if(info.value.divisionCode){
			return {
				province:info.value.province,
				city:info.value.city,
				divisionCode:info.value.divisionCode,
			}
		}else{
			return {
				province:Defaultlocation.province,
				city:Defaultlocation.city,
				divisionCode:Defaultlocation.divisionCode,
			}
		}
	})
	
	function update(data:any) {
		info.value = data.info;
		account.value = data.account;
		tokenInfo.value = { token: data.token, expire: data.expire }
		currentLocal.value = {
			lon: data.info.lon,
			lat: data.info.lat,
			province: data.info.province,
			city: data.info.city,
			area: '',
			divisionCode: data.info.divisionCode
		};

		save();
		saveToken();
	}
	function save(){
		uni.setStorageSync(userStoreKey, { info:unref(info),token:unref(tokenInfo),account:unref(account),currentLocal:unref(currentLocal) });
	}
	function saveToken() {
		uni.setStorageSync(tokenStoreKey, unref(tokenInfo));
	}

	async function tryLogin() {
		return new Promise(async (r, j) => {
			try {
				let code = await getWxLoginCode();
				let data = await ajax.post("/user/trylogin", { code });
				update(data);
				r(true);
			} catch (e) {
				j(e);
			}
		})
	}
	
	async function syncCurrentLoc() {
		const loc = await getCurrentLoc();
		currentLocal.value = loc
		//如果user中的province,city和divisionCodeo为空，则同步
		// if(!info.value.divisionCode){
		// 	info.value.divisionCode = loc.divisionCode
		// }
		save();

	}
	function getWxLoginCode() {
		return new Promise((resolve, reject) => {
			// uni.login非异步，
			uni.login({
				provider: 'weixin',
				success: res => {
					resolve(res.code);
				},
				fail: err => {
					reject(err);
				}
			});
		})
	}
	return {
		info, tokenInfo, account, currentLocal,tryLocal,
		syncCurrentLoc, tryLogin,isLogin,isMember,isAdmin
	}
})