import { defineStore } from 'pinia';
import ajax from '../common/ajax';
import { userStore } from './user';
import { ref } from 'vue';
const DivisonCityMapKey = "__divisonCityMap"
const defaultDivisonCityMap = uni.getStorageSync<IDivisonCityMap>(DivisonCityMapKey) || []


export const appStore = defineStore('app',
	() => {
		const enums = ref<Record<string, Record<string,number>>>({});
		const assets = ref<{
			name : string,
			key : string,
			/*内容 {name:string,url:string,link:wxminip:pages/xxx/xxx} */
			content : string,
			intro : string
		}[]>([])
		const sys = ref<Record<string, string>>({})
		const divisonCityMap = ref<IDivisonCityMap>(defaultDivisonCityMap)
		
		const initDoing = ref(false)

		async function init(force = false) {
			initDoing.value = true;
			getBase();
			const user = userStore();
			
			if (!user.isMember || force) {
				try {
					await user.tryLogin();
				} catch (e) {
					
					uni.showToast({
						title: e.message,
						icon:'error'
					})
					if(e.message!=="NET"){
						uni.reLaunch({
							url: "/pages/regMember/regMember"
						})
					}
				}
				try{
					user.syncCurrentLoc()
				}catch{
					console.log('未获得lbs')
				}
			}
			initDoing.value = false;
		}
		async function getBase() {
			let base = await ajax.get<any>('/base');
			enums.value = base.enums;
			assets.value = base.assets;
			sys.value = base.sys;
			
			if(!divisonCityMap.value.length){
				ajax.get<IDivisonCityMap>('/base/divisonCityMap').then(res=>{
					divisonCityMap.value = res;
					uni.setStorageSync(DivisonCityMapKey,res)
				})
			}

			
		}
		
		function tryEnum(type:"accountType"|"cityTeamType"|"commentType"|"likeType"|"matchType"|"partyPaid"|"partySize"|"partyTime"|"partyType"|"state"|"userLevel"|"venueAble"|"venuePaid"|"venueParking"|"venueSize"|"venueType",value:string|number|null|undefined){
			if(value===null||value===undefined) return "未知";
			return enums.value[type]?.[value] || "未知";
			
		}
		//this.initDoing=false;
		return {
			enums, assets, sys, tryEnum,divisonCityMap,
			init,getBase
		}
	});