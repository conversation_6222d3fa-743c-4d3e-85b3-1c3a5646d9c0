<template>
	<view class="flex f-lg mp10">
		<view class="f-b">
			约毽
		</view>
		<view class="mpl10 f-color-gray">
			Party
		</view>
	</view>	
		<view class="card">

			<view class="week-list">
				<view class="item-1 g-center hotpoint" v-for="item in d" :key="item.name" :class="{gray:item.count==0}">
					{{item.name}}
				</view>
			</view>
			
			<view v-if="list.length" class="party-list">
				<view class="item" v-for="item in list" :key="item.id">
					<view class="flex flex-center" style="gap: 10px;">
						<view class="">
							{{ item.beginDate_s }}
						</view>
						<view class="address">
							{{item.address}}
						</view>
					</view>
					<view class="flex flex-center" style="gap: 4px;">
						<view class="">
							{{$filters.tryEnum('partyType',item.type)}}
						</view>
						<my-user-avatar style="display: flex;" :data="item.user" size="mini"></my-user-avatar>
					</view>
				</view>
			</view>
			
			<view v-else class="pd10">
				<view class="g-center f-color-gray f-sm">
					暂时未有约毽活动
				</view>
			</view>
		</view>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import ajax from '../common/ajax';
import { userStore } from '../store/user';
import { formatDate, weeks } from '../common/funs';
import { appStore } from '../store';
const app = appStore();
const d = ref([
	{
		name:'周七',
		date:'2025/05/06',
		count:0
	}
])
const list = ref<IParty[]>([]);

const us = userStore();
watch(()=>us.tryLocal.divisionCode,(n,o)=>{

	load();
		
},{immediate:true})

async function load(){
	const res = await ajax.get<Ipagen<IParty>>('/party',{
		divisionCode:us.tryLocal.divisionCode,
		dateStart:formatDate(new Date()),
		pageSize:5,
	});
	res.list.forEach(v=>{
		
		v.beginDate_s = v.beginDate.slice(5,10);
		v.beginDate = v.beginDate.replaceAll('-','/')
	})
	list.value = res.list;
	d.value = [];
	const now = new Date();
	[0,1,2,3,4,5,6].forEach(item=>{
		now.setDate(now.getDate()+item);	
		let date=now.toLocaleDateString();
		d.value.push({
			name:weeks[now.getDay()],
			date:date,
			count:res.list.filter(item=>new Date(item.beginDate).toLocaleDateString()==date).length
		})
	})

}
</script>

<style scoped lang="scss">
	.week-list{
		display: flex;
		gap: 9px;
		&>view{
			flex:1;
			line-height: 200%;
			text-align: center;
			border-radius: 6px;
			background: rgba(196, 196, 196, 0.15);
			position: relative;
			&.hotpoint::after{
				content: "";
				width: 8px;
				height: 8px;
				border-radius: 8px;
				display: block;
				position: absolute;
				right: 2px;
				bottom: 2px;
				background-color: $uni-color-success;
			}
			&.gray{
				// filter: grayscale(1);
				background-color: $uni-bg-color-grey;
			}
		}
	}
	.party-list{
		display: flex;
		flex-direction: column;
		gap: 10px;
		padding: 10px 0;
		.item{
			display: flex;
			justify-content: space-between;
			align-items: center;
			background-color: $uni-bg-color-grey;
			line-height: 1.5;
			padding: 5px;
			.address{
				width: 78%;
				      display: -webkit-box; /* 设置为WebKit内核的弹性盒子模型 */
				      -webkit-box-orient: vertical; /* 垂直排列 */
				      -webkit-line-clamp: 2; /* 限制显示两行 */
				      overflow: hidden; /* 隐藏超出范围的内容 */
				      text-overflow: ellipsis; /* 使用省略号 */
			}
		}
	}

</style>