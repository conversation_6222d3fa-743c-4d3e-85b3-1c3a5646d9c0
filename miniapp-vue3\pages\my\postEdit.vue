<template>
	<view>
		<my-page-header></my-page-header>
		<view style="padding: 30rpx;">
			<view style="position: relative;">
				<textarea maxlength="contentLimit" :auto-height="true" class="my-textarea" v-model="d.model.content" style="width: 100%;" value="" placeholder="请输入文字内容" />
				<view style="position: absolute;bottom: 10rpx;right: 0;" class="f-time">
					{{ d.model.content.length }}/{{ contentLimit }}
				</view>
			</view>
			<view style="padding-top: 30rpx;">
				<uploadImages :showCover="false" provider="qn" path="user" :max="6" v-model="d.model.images"></uploadImages>
			</view>
		</view>
		<view class="center" style="padding: 30rpx;">
			<button class="my-button big full" style="" @tap="save" :loading="d.doing" :disabled="d.doing || d.model.content.length == 0">发布</button>
		</view>
	</view>
</template>

<script lang="ts" setup>
import { reactive } from 'vue'
import ajax from '../../common/ajax'
import { wxContentCheck } from '../../common/funs'
import uploadImages from "@/components/upload-images.vue"

const contentLimit = 200

const d = reactive({
	model: {
		content: '',
		images: []
	},
	doing: false
})

async function save() {
	try {
		d.doing = true
		await wxContentCheck(d.model.content, 'txt')
		const postData = {
			content: d.model.content,
			images: d.model.images.map((v: any) => v.url).join(',')
		}
		await ajax.post('/my/post', postData)
		
		if (postData.images.length != 0) {
			const [err, res] = await uni.showModal({
				title: '动态中的图片是否加到相册中?'
			})
			if (res.confirm) {
				await ajax.post('/my/gallery', { imageUrls: d.model.images.map((v: any) => v.url) })
			}
		}
		uni.navigateBack()
	} catch (e) {
		console.error('发布动态失败', e)
	} finally {
		d.doing = false
	}
}
</script>

<style lang="scss">
	page{
		background-color:$uni-color-warning;
	}
</style>
