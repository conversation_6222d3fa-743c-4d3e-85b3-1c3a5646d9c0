<template>
	<view class="item" >
		<view class="item-date">
			<text class="date-month">{{m.month}}月</text> 
			<view class="date-day">{{m.day}}</view>
		</view>
		<view class="item-content">
			<view v-if="allowEdit" class="delBtn iconfont icondel f-color-light lg" @tap="del"></view>
			<text class="content">
				{{m.content}} <text class="item-time">{{m.time}}</text>
			</text>
			<view class="images" v-if="m.images.length!=0">
				<image lazy-load class="post-img g-hover" mode="aspectFill" @tap="viewImage(m.images,imgIdx)" v-for="img,imgIdx in m.images" :src="img | imgResize" :key="imgIdx"></image>
			</view>
		</view>
	</view>
</template>

<script lang="ts" setup>
import { ref, watch, toRefs } from 'vue'
import { formatDate } from '../common/funs'

const props = defineProps({
	d: { type: Object, required: true },
	allowEdit: { type: Boolean, default: false }
})
const emit = defineEmits(['del'])

const m = ref<any>({})

watch(() => props.d, (v) => {
	if (!v) return
	if (!v.month) {
		const s = formatDate(v.updatedAt, 'MM,dd,hh:mm').split(",")
		v.month = s[0]
		v.day = s[1]
		v.time = s[2]
	}
	if (!(v.images instanceof Array)) {
		v.images = v.images ? v.images.split(',') : []
	}
	m.value = v
}, { immediate: true })

function viewImage(urls: string[], idx = 0) {
	wx.previewImage({
		urls,
		current: urls[idx]
	})
}

function del() {
	emit('del', m.value)
}
</script>

<style lang="scss">
	.item-time{
		font-size: $uni-font-size-sm;
		padding-left: 20rpx;
		opacity: .5;
	}
	.item{
		display: flex;align-items: flex-start;
	}
	.item-date{
		width: 100rpx;
		color: #FFFFFF;
		text-align: right;
	}
	.item-content{
		flex: 100%;
		margin-left: 20rpx;
		padding:0rpx 10rpx 10rpx 10rpx;
		position: relative;
		//background: rgba($color: #fff, $alpha: .1);
		.delBtn{
			position: absolute;
			bottom: 0;
			right: 0;
		}
	}
	.date-month{
		font-size: .8em;
	}
	.date-day{
		font-size: 1.5em;
		font-weight: bold;
	}
	.post-img{
		width:100rpx;
		height: 100rpx;
		margin: 10rpx 10rpx 0rpx 0;
		border-radius: 4rpx;
	}
</style>
