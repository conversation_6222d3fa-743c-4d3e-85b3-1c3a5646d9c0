

@import '@/uni.scss';
@import "iconfont.css";

@font-face {
    font-family: 'OPPOSans';
    font-style: normal;
    src: local('OPPOSans'), url('https://fonts.cdnfonts.com/s/66596/OPPOSansMedium.woff') format('woff');
}


page,body {
	background-color: $uni-bg-color;
	color: $uni-text-color;
	font-family: 'OPPOSans';
	box-sizing: border-box;
	font-size: $uni-font-size-base;
	box-sizing: border-box;
}
page{
	padding:0;
	width: 100%;

}
button{border-radius: 40upx;}

view,view:before,view:after,navigator{
	box-sizing: border-box;
}
.container{
	padding:0 8px;
}

navigator{
	padding:5px 0;
}

.tabs{
	border-radius: 12upx;
	padding: 0upx 30upx;
	display: flex;
	justify-content: space-around;
	align-items: stretch;
	margin: auto;
	.tab{
		
		font-weight: 600;
		font-size: $uni-font-size-lg;
		position: relative;
		line-height: 2;
		padding: 0 30upx;
		&::after{
			content: "";
			width: 60px;
			height: 2px;
			border-radius: 2px 2px 0 0;
			background: $uni-color-success 100%;
			opacity: 0;
			position: absolute;
			margin-left: -30px;
			bottom: 0;
			left:50%;
		}
		&.active{
			color: $uni-color-success;
			&::after{
			opacity: 1;
			transition: all .3s;
			
			}
		}
	}
}


.mp10{
	margin: 20upx;
}
.mpt10{
	margin-top: 20upx;
}
.mpl10{
	margin-left: 20upx;
}
.mpb10{
	margin-bottom: 20upx;
}
.pd10{
	padding: 20upx;
}
.pd20{
	padding: 40upx;
}
.page-container{
	padding: 0upx 30upx;
}
.page-title{
	font-size:$uni-font-size-lg;
	color:#333;
	text-align: center;
	padding:50upx 0 30upx 0;
	position: relative;
	z-index:100;
	 font-weight: 600;
}
.ext{
	 color: #5d6064;
	 font-size: $uni-font-size-sm;
	 font-weight: 100;
}
.disabel{
	opacity: .5;
	pointer-events: none;
}
.card{
	border-radius: 10px;
	padding:30upx;
	background-color: $uni-bg-color-grey;
	//background: linear-gradient(-45deg, $uni-bg-color-grey 0, $uni-bg-color-hover 100%);
	// border: 1px solid #99999911;
	// box-shadow: 4px 4px 4px #00000022;
	// background: linear-gradient(90.00deg, rgba(42, 47, 55, 1),rgba(42, 47, 55, 0) 100%);
	overflow: hidden;
	margin-bottom: 20upx;
	width: 100%;
	color:#fff
}
.line{
	border-bottom:1px solid $uni-border-color;
	margin: 20upx 0;
}

.row{
	border-bottom:1px solid #EBEBEB;
	//margin:0 30upx;
	padding:26upx 0;
	&:last-child{
		border: 0;
	}
}

.page-bottom{
	position: fixed;
	bottom: 0;
	width: 100%;
	padding:0upx 0upx;
	display: flex;
	// gap: 30upx;
	background-color: $uni-bg-color-grey;
	z-index: 10;
	&>button,&>view,.my-btn{
		flex:1px;
		margin: 0;
		padding: 0;
		border-radius: 0;
		height: 48px;
		display: flex;
		line-height: auto;
		font-size: $uni-font-size-base;
		justify-content: center;
		align-items: center;
		&::after {
			border-radius: 0;
		}
	}
	left: 0;
}
.page-bottom-fix{
	padding-bottom: 80px;
}

.content{padding:30upx}
.f-color{
	color: $uni-text-color;
}
.f-color-success{
	color: $uni-color-success;
}
.f-color-white{
	color: #fff;
}
.f-color-light{
	color: $uni-text-color-inverse
}
.f-color-gray{
	color: $uni-text-color-grey;
}
.f-color-primary{
	color: $uni-color-primary;
}
.f-lg{
	font-size: $uni-font-size-lg;
}
.f-lgx2{
	font-size: $uni-font-size-lg*2;
}
.f-b{
	font-weight: bold;
}
.f-sm{
	font-size: $uni-font-size-sm;
}
.f-base{
	font-size: $uni-font-size-base;
}
.f-title{
	font-size: 20px;
	font-weight: 600;
	color: #fff;
}
.f-ext{
	font-size: $uni-font-size-base;
	font-weight: 100;
}

.flex{
	display: flex;
	.item-1{
		flex:1
	}
}
.flex-end{
	display: flex;
	justify-content: flex-end;
}
.flex-between{
	justify-content: space-between;
}
.flex-evenly{
	justify-content: space-evenly;
}
.flex-center{
	align-items: center;
}
.flex-gap5{
	gap: 5px;
}
.flex-gap10{
	gap: 10px;
}
.flex-center-all{
	display: flex;align-items: center;justify-content: center;
	width: 100%;
	height: 100%;
}
.g-center{
	text-align: center;
}
.g-gray{
	opacity: .7;
}
.list {
	background-color: #fff;
	&.between{
		justify-content: space-between;
		flex-shrink: 0;
		.item{
			&>view:first-child{
				flex-grow: 1;
			}
			&>view:last-child{
				display: flex;
			}
		}
	}
	.item {
		border-bottom: 1px solid #EBEBEB;
		margin: 0 20upx;
		padding: 30upx 0;
		display: flex;
		justify-content: space-between;
		align-items: center;
		label{
			font-weight: 400;
			width: 40%;
			color: #999;
		}
		&:last-child{
			border-bottom:0
		}
		view{
		}
		view,label{
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			
		}
		.arrow{
			margin-left: 10upx;
		}
	}
}

.input{
	border-bottom: 1px solid $uni-border-color;
}

.link{
	color: $uni-color-success;
	font-family: OPPOSans;
	font-weight: 200;
	line-height: 2;
}
.btn{
	background: $uni-bg-color-grey;
	border-radius: 12px;
	padding: 5px 20px;
	display: inline-block;
	text-align: center;
	&.cy{
		border-radius: 24px;
	}

	&.icon{
		padding: 6px;
		margin: 2px;
		font-size: 24px;
		color: #fff;
	}

	&.hotpoint{
		position: relative;
		&::before{
			display: block;
			content: "";
			background-color: #ff0000;
			width: 8px;
			height: 8px;
			position: absolute;
			right: 8px;
			top: 8px;
			z-index: 2;
			border-radius: 4px;
		}

	}
	
	&.light{
		color: $uni-bg-color;
		background: rgba(224, 254, 16, 1);
	}
	/*圆形箭头*/
	&.cy-right-arrow{
		height: 30px;
		min-width: 30px;
		border-radius: 30px;
		color: $uni-bg-color;
		background: rgba(224, 254, 16, 1);
		line-height: 30px;
		padding: 0px;
		&.pd{
			padding: 0 10px !important;
		}
		&:after{
			content: "\e76c";
			font-family: "iconfont";
			color: $uni-color-primary;
			font-size: 16px;
		}
	}
}

.nowrap{
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
}

.my-title {
	position: relative;
	line-height: 1.8;
	padding-left: 20upx;
	font-weight: 400;
	&::before {
		position: absolute;
		display: inline-block;
		top: 16upx;
		left: 0upx;
		content: "";
		width: 5px;
		height: 18px;
		background: $uni-color-warning;
		border-radius: 8upx;
	}
	
	&.sm {
		font-size: $uni-font-size-sm;
		&::before {
			top:  10upx;
			height: 24upx;
		}
	}
	&.big {
		font-size: $uni-font-size-lg;
		line-height: 2;
		&::before {
			top:  15upx;
		}
	}
	&.in-card{
		left: -20upx ;
		padding-left: 20upx;
		&.sm{
			margin-top: -10upx;
		}
		&.lg{
			left: -30upx ;
			padding-left: 28upx;
			// &:before {
			// 	left: -30upx ;
			// }

		}
	}
}
	.my-input{
		padding: 10rpx;
		color:$uni-text-color;
		// background: rgba($color:#fff, $alpha: .05);
		// border:1px solid rgba($color:#fff, $alpha: .5);
		width: auto;
		// &::after{
		// 	content: "1";
		// 	display: inline;
		// }
		&.light{
			background: rgba($color:#fff, $alpha: .85);
			border:1px solid rgba($color:#fff, $alpha: .9);
		}
		&.dark{
			background: rgba($color:#fff, $alpha: .1);
			border:1px solid rgba($color:#fff, $alpha: .2);
		}
	}
	.my-textarea{
		padding: 10rpx;
		color:$uni-text-color;
		background: rgba($color:#fff, $alpha: .05);
		width: auto;
		border:1px solid rgba($color:#fff, $alpha: .1);
	}
	.my-tag{
		display: inline-block;
		color: #fff;
		background-color: $uni-bg-color-hover;
		padding: 0 20upx;
		line-height: 2.2;
		border-radius: 4upx;
		margin:4upx;
		
		&.border{
			border: 1px solid $uni-bg-color-grey;
		}
		&>label{
			border-radius: 3upx 0  0 3upx;
			background: rgba(255,255,255,.2);
			display: inline-block;
			flex-shrink: 1;
			height: 100%;
			margin-left: -20rpx;
			padding:0 10rpx 0 20rpx;
			margin-right: 10rpx;
			opacity: .5;
		}
		&.sm{
			font-size:$uni-font-size-sm;
			line-height: 1.6;
		}
		&.danger{
			color: $uni-color-error;
			background-color: rgba($color: $uni-color-error, $alpha: .1);
		}
		&.success{
			color: $uni-color-success;
			background-color: rgba($color: $uni-color-success, $alpha: .1);
		}
		&.warn{
			color: $uni-color-warning;
			background-color: rgba($color: $uni-color-warning, $alpha: .1);
		}
		&.light{
			color: rgba($color: #55ff00, $alpha: .8);
			background-color: rgba($color: #55ff00, $alpha: .1);
		}
		&.danger-light{
			color: rgba($color: #fff, $alpha: .7);
			background-color:  rgba($color: $uni-color-error, $alpha: .7)
		}
	}
	.my-btn {
		background-color: $uni-bg-color-grey;
		color: #fff;
		font-size: $uni-font-size-base;
		padding: 20upx 30upx;
		border-radius:6px;
		// border: 1px solid $uni-bg-color-grey;
		display: inline-block;
		text-align: center;
		&:active {
			opacity: .7;
		}
		.iconfont{
			margin: 0 8rpx;
			line-height: 1;
			padding-bottom: 6px;
		}
		&:active {
			background: change-color($color: $uni-color-primary, $alpha: .6);
			border-radius: 8upx;
		}
	
		&::after {
			border-color: $uni-color-primary;
		}
		&.sm{
			padding:auto 28upx;
			font-size: $uni-font-size-base;
			line-height: 2;
			.iconfont{
				margin: 0 4rpx;
				line-height: 1;
			}
		}
		&.warn{
			background:$uni-color-warning;
			&::after {
				border-color: $uni-color-warning;
			}
			&:active {
				background: change-color($color: $uni-color-warning, $alpha: .6);
			}
			
			&.plain {
				background: transparent;
				color: $uni-color-warning;
				&:active {
					background: $uni-color-warning;
					color: #fff;
				}
			}
		}
		&.gray{
			background:$uni-bg-color-hover;
			color: #fff;
			&::after {
				border-color:$uni-bg-color-hover;
			}
			&:active {
				opacity: .7;
			}
		}		
		&.white{
			background:change-color($color: #fff, $alpha: 1);
			color:$uni-text-color;
			&::after {
				border-color: change-color($color: #fff, $alpha: 1);
			}
			&:active {
				background: change-color($color: #fff, $alpha: .5);
			}
			
			&.plain {
				background: transparent;
				color: #fff;
				&:active {
					background:change-color($color: #fff, $alpha: .2);
					color: #fff;
				}
			}
		}

		&.light{
			color: rgba(255,255,255,.8);
			background:rgba(255,255,255,.2);
			&.plain {
				background: transparent;
				color: #fff;
				&:active {
					background:change-color($color: #fff, $alpha: .2);
					color: #fff;
				}
			}
		}

		&.success{
			color: $uni-color-primary;
			background:$uni-color-success;
			&::after {
				border-color: $uni-color-success;
			}
			&:active {
				background: change-color($color: $uni-color-success, $alpha: .6);
			}
			
			&.plain {
				background: transparent;
				color: $uni-color-success;
				&:active {
					background: $uni-color-success;
					color: #fff;
				}
			}
		}
	
		&.big {
			line-height: 2.2;
			font-size:$uni-font-size-lg*2;
			padding: 10rpx 50rpx;
		}
	
		&.lg {
			width: auto;
			border-radius: 8upx;
			font-size: $uni-font-size-lg;
			line-height: 2;
		}
	
		&.full {
			width: auto;
			display: block;
			border-radius: 8upx;
			font-size: $uni-font-size-lg;
			text-align: center;
			line-height: 2.2;
		}
	
		&.plain {
			background: transparent;
			color: $uni-color-primary;
	
			&::after {
				border-radius: 10upx;
			}
	
			&:active {
				background: $uni-color-primary;
				color: #fff;
			}
		}
		&.cy{
			border-radius: 36upx;
		}
	}
	
	.line-1 {
		border-bottom: 1upx solid rgba(236, 239, 247, 0.4);;
	}
	
	.line-3 {
		border-bottom: 1upx solid #f6f6f6;
	}
	
	.line-2 {
		border-bottom: 1upx dashed rgba(236, 239, 247, 0.1);;
	}
	
.my-popup{
	height: 70vh;width: 80vw;
	
	background: $uni-bg-color;
	padding: 20rpx;
	border-radius: 10px;
}

.my-box{
	display: flex;
	flex-wrap: wrap;
	gap: 10px;
}