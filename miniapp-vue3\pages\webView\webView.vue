<template>
	<view>
		<web-view title="xxx" :src="d.url"></web-view>
	</view>
</template>

<script lang="ts" setup>
import { reactive } from 'vue'
import { onLoad, onShareAppMessage } from '@dcloudio/uni-app'

const d = reactive({
	url: ''
})

onLoad((param: any) => {
	d.url = param.url
})

onShareAppMessage(() => {
	return {
		title: '毽球风云:毽球教程',
		path: "/pages/Teaching/Teaching"
	}
})
</script>

<style lang="scss">

</style>
