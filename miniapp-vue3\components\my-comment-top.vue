<template>
	<view class="">
		<view class="row-flex-around" style="margin-top: 20rpx;align-items: flex-start;">
			<view class="iconfont iconaddMsg auto" style="font-size: 60rpx;color: rgba(255,255,255,.5);"></view>
			<view :class="{'comment-hidden':d.inputHidden}" style="position: relative;width: 100%;">
				<textarea class="my-textarea comment-input" :disable-default-padding="true" confirm-type="send" @focus="d.inputHidden=false"
					:maxlength="d.limit" v-model="d.model.content"
					placeholder="评论"></textarea>
				<view class="my-btn success comment-submit" @tap="saveComment"
					:class="{'g-disabled':d.model.content.length>d.limit || d.doing}">
					提交：
					<text class="f-sm" style="color: #76a905;">{{d.model.content.length}}/{{d.limit}}</text>
				</view>
			</view>
		</view>
		<view class="light f-sm" style="margin-top: 20rpx;" >

			<view class="item line-2" v-for="(item, idx) in d.list" :key="idx">
				<view class="flex flex-center flex-between">
					<my-user-avatar size="mini" :data="item.user" showName nameLine="" allowLink></my-user-avatar>
					<text class="f-time ">{{item.updatedAt}}</text>
				</view>
				<view class="comment-content">{{ item.content  }}</view>
			</view>
		</view>
		<navigator class="f-color-success" :url="`/pages/comment/comment?targetId=${targetId}`" style="text-align: right;" >
			查看留言>
		</navigator>
	</view>
</template>

<script lang="ts" setup>
	import { reactive, ref, watch } from 'vue';
	import { appStore } from '../store';
	import ajax from '../common/ajax';
	import { wxContentCheck } from '../common/funs';
	const app = appStore();
	const props = defineProps(['targetId'])
	const paging = ref();
	watch(() => props.targetId, () => {
		if(props.targetId){
			loadTop();
		}
		
	},{
		immediate:true
	})
	const d = reactive({
		list:[] as any[],
		limit: 256,
		inputHidden: true,
		doing: false,
		model: {
			content: ''
		}
	})

	async function loadTop() {
		
		var res = await ajax.get<any>(`/comment/list?targetId=${props.targetId}`, { pageSize: 5 });
		d.list = res.list;	
	}
	function goUser(id : string) {
		uni.navigateTo({
			url: "/pages/User/User?id=" + id
		})
	}
	async function saveComment() {

		d.doing = true;
		try {
			await wxContentCheck(d.model.content, 'txt');
			await ajax.post('/comment', { targetId: props.targetId, content: d.model.content });
			d.model.content = "";
			d.inputHidden = false;
			setTimeout(() => {
				uni.showToast({
					title: "提交成功"
				})
			}, 200)

			loadTop();
		} finally {
			d.doing = false;
		}
	}
</script>

<style lang="scss">
	.comment-input {
		height: 160rpx;
		transition: height .3s;
		margin-top: 10rpx;
	}

	.item {
		padding: 10rpx 0;
	}

	.comment-content {
		padding-left: 38px;
	}

	.comment-submit {
		letter-spacing: 1rpx;
		background: rgba(0, 0, 0, .2);
		color: #fff;
		padding: 0 10rpx;
		position: absolute;
		bottom: 10rpx;
		right: 10rpx;
		z-index: 10;
		transition: opacity .3s;
		line-height: 2;
	}

	.comment-hidden {
		.comment-input {
			height: 60rpx;
		}

		.comment-submit {
			opacity: 0;
			pointer-events: none;
		}
	}
</style>